# ===================================
# IDE 和编辑器文件
# ===================================
.idea/
.trae/
.vscode/
*.swp
*.swo
*~
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ===================================
# 操作系统生成的文件
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===================================
# 日志文件
# ===================================
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# 运行时数据
# ===================================
pids
*.pid
*.seed
*.pid.lock

# ===================================
# 依赖目录
# ===================================
node_modules/
jspm_packages/
bower_components/
web_modules/

# ===================================
# 包管理器文件
# ===================================
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.pnpm-store/
.yarn-integrity
*.tgz

# ===================================
# 环境变量文件
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===================================
# 构建输出目录
# ===================================
dist/
build/
out/
lib-cov/
build/Release

# ===================================
# UmiJS 相关
# ===================================
.umi/
.umi-test/
.umi-production/
.umi-dev/

# ===================================
# React/Next.js 相关
# ===================================
.next/
.nuxt/

# ===================================
# 测试覆盖率
# ===================================
coverage/
*.lcov
.nyc_output
.jest/

# ===================================
# 缓存目录
# ===================================
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo

# ===================================
# 临时文件夹
# ===================================
tmp/
temp/
.temp/

# ===================================
# 其他工具生成的文件
# ===================================
.node_repl_history
.grunt
.lock-wscript
.fusebox/
.dynamodb/
.tern-port
.vscode-test
.serverless/

# ===================================
# 文档生成工具
# ===================================
.vuepress/dist
.docusaurus

# ===================================
# Storybook
# ===================================
.out
.storybook-out
