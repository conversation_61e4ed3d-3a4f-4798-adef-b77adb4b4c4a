# Ant Design Pro

This project is initialized with [Ant Design Pro](https://pro.ant.design). Follow is the quick guide for how to use.

## Environment Prepare

Install `node_modules`:

```bash
npm install
```

or

```bash
yarn
```

## Provided Scripts

Ant Design Pro provides some useful script to help you quick start and build with web project, code style check and test.

Scripts provided in `package.json`. It's safe to modify or add additional script:

### Start project

```bash
npm start
```

### Build project

```bash
npm run build
```

### Check code style

```bash
npm run lint
```

You can also use script to auto fix some lint error:

```bash
npm run lint:fix
```

### Test code

```bash
npm test
```

## More

You can view full document on our [official website](https://pro.ant.design). And welcome any feedback in our [github](https://github.com/ant-design/ant-design-pro).

AntdPro 5.0 版本

# ⚠️ 注意

test: http://**************:31002/rule http://**************:31002/rule/swagger-ui/index.html#/ app.tsx (配置模拟用户数据) └── Header (左侧菜单头部) └── PageHeader (页面头部) └── PersonalCenter (模拟用户显示模块) ├── DataStats (数据统计) ├── MessageNotification (消息通知) └── AvatarDropdown (用户头像下拉菜单)

模拟登录和用户展示模块代码位置如下：

1. 模拟登录核心实现：

   - 文件: myapp/src/app.tsx
   - 函数: getInitialState()
   - 关键代码: 直接返回模拟用户数据，包含 name 和 avatar 字段

2. 用户展示组件：

   - 文件: myapp/src/components/RightContent/AvatarDropdown.tsx
   - 组件: AvatarDropdown
   - 功能: 处理用户头像展示和下拉菜单

3. 用户中心样式：

   - 文件: myapp/src/components/PersonalCenter/index.tsx
   - 类名: mock-user-center, mock-user-avatar-section

4. 全局配置：

   - 文件: myapp/src/app.tsx
   - 配置: layout 中的 avatarProps 将用户数据传递给 AvatarDropdown 组件
