import { ProLayoutProps } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: ProLayoutProps & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  // 拂晓蓝
  "colorPrimary": "#0AAC79",
  layout: 'side',
  contentWidth: 'Fluid',
  fixedHeader: false,
  fixSiderbar: true,
  colorWeak: false,
  title: '规则引擎 \n EverModel',
  pwa: true,
  logo: '/logo.svg',
  iconfontUrl: '',
  footerRender: false,
  siderMenuType: 'sub',

  // {
  //   "navTheme": "light",
  //   "colorPrimary": "#1890ff",
  //   "layout": "side",
  //   "contentWidth": "Fluid",
  //   "fixedHeader": false,
  //   "fixSiderbar": true,
  //   "pwa": true,
  //   "logo": "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg",
  //   "footerRender": false,
  //   "token": {},
  //   "splitMenus": false,
  //   "siderMenuType": "sub"
  // }
};

export default Settings;
