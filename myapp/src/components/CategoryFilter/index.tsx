import React from 'react';
import { Segmented } from 'antd';
import './index.less';

export interface CategoryItem {
  id: string;
  name: string;
  icon?: string;
  children: CategoryItem[] | null;
}

interface CategoryFilterProps {
  categories: CategoryItem[];
  selectedType: string;
  selectedSubType: string;
  onTypeChange: (typeId: string) => void;
  onSubTypeChange: (subTypeId: string) => void;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  categories,
  selectedType,
  selectedSubType,
  onTypeChange,
  onSubTypeChange,
}) => {
  const currentCategory = categories.find((cat) => cat.id === selectedType);
  // 构造Segmented options
  const typeOptions = [
    { value: '', label: '全部' },
    ...categories.map((category) => ({ value: category.id, label: category.name })),
  ];
  return (
    <div>
      {/* 一级分类 */}
      <div style={{ marginBottom: 16 }}>
        <Segmented
          value={selectedType}
          onChange={(val) => onTypeChange(val as string)}
          className="custom-filter-segmented"
          options={typeOptions}
        />
      </div>
      {/* 二级分类 */}
      {currentCategory?.children && currentCategory.children.length > 0 && (
        <div style={{ marginBottom: 16 }}>
          <Segmented
            value={selectedSubType}
            onChange={(val) => onSubTypeChange(val as string)}
            className="custom-filter-segmented"
            options={[{ value: '', label: '全部' }, ...currentCategory.children.map((sub) => ({ value: sub.id, label: sub.name }))]}
          />
        </div>
      )}
    </div>
  );
};

export default CategoryFilter; 