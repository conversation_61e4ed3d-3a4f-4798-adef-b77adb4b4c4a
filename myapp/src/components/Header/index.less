.custom-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #f0f0f0;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    max-width: 1200px;
    margin: 0 auto;
  }

  .header-logo {
    width: 32px;
    height: 32px;
    object-fit: contain;
  }

  .header-title {
    margin: 0 !important;
    color: #1f2937;
    font-weight: 600;
    font-size: 20px;
    line-height: 64px;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }

  .header-icon {
    width: 24px;
    height: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0.7;

    &:hover {
      opacity: 1;
      transform: scale(1.1);
    }
  }
}

@media (max-width: 768px) {
  .custom-header {
    padding: 0 16px;

    .header-title {
      font-size: 18px;
    }

    .header-logo {
      width: 28px;
      height: 28px;
    }

    .header-icon {
      width: 20px;
      height: 20px;
    }
  }
}