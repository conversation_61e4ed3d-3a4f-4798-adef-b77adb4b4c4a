import { Layout } from 'antd';
import React from 'react';
import './index.less';

const { Header: AntHeader } = Layout;

interface HeaderProps {
  className?: string;
  collapsed?: boolean;
}

const Header: React.FC<HeaderProps> = ({ className = '', collapsed = false }) => {
  return (
    <AntHeader className={`custom-header ${className}`}>
      <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
        <img
          src={collapsed ? '/logo.svg' : '/logo_full.svg'}
          alt="Logo"
          style={{
            width: collapsed ? '34px' : '167px',
            height: '34px',
            objectFit: 'contain',
          }}
        />
      </div>
    </AntHeader>
  );
};

export default Header;
