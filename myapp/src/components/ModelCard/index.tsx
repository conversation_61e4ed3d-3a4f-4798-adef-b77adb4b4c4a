import React from 'react';
import { Card, Avatar, Switch, Button } from 'antd';

export interface ModelCardProps {
  id: string;
  name: string;
  description: string;
  icon?: string;
  isOnline: number;
  onDetailClick?: () => void;
  onStatusChange?: (checked: boolean) => void;
  showSwitch?: boolean;
  showDetailBtn?: boolean;
  style?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
}

const ModelCard: React.FC<ModelCardProps> = ({
  name,
  description,
  icon,
  isOnline,
  onDetailClick,
  onStatusChange,
  showSwitch = true,
  showDetailBtn = false,
  style = {},
  bodyStyle = {},
}) => {
  return (
    <Card
      hoverable
      style={{ display: 'flex', flexDirection: 'column', ...style }}
      styles={{
        body: { flex: 1, display: 'flex', flexDirection: 'column', padding: '20px', ...bodyStyle },
      }}
      onClick={onDetailClick}
    >
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px', flex: 1 }}>
        <Avatar src={icon} size={48} />
        <div style={{ flex: 1, minWidth: 0 }}>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '4px',
            }}
          >
            <h4
              style={{ margin: 0, fontSize: '16px', fontWeight: 500, color: 'rgba(0, 0, 0, 0.88)' }}
            >
              {name}
            </h4>
            {showSwitch && (
              <div onClick={(e) => e.stopPropagation()}>
                <Switch size="small" checked={isOnline === 1} onChange={onStatusChange} />
              </div>
            )}
          </div>
          <p
            style={{
              margin: 0,
              color: 'rgba(0, 0, 0, 0.45)',
              fontSize: '14px',
              lineHeight: '1.3',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {description}
          </p>
        </div>
      </div>
      {showDetailBtn && (
        <div
          style={{
            borderTop: '1px solid #f0f0f0',
            marginTop: '8px',
            paddingTop: '8px',
            display: 'flex',
            justifyContent: 'flex-start',
            alignItems: 'center',
          }}
        >
          <Button
            type="link"
            size="small"
            style={{ padding: 0 }}
            onClick={(e) => {
              e.stopPropagation();
              if (onDetailClick) onDetailClick();
            }}
          >
            详情
          </Button>
        </div>
      )}
    </Card>
  );
};

export default ModelCard;
