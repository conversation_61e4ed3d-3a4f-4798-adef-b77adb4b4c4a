.page-header {
  position: relative;
  z-index: 10;
  background: transparent;
  padding: 16px 20px;
  border-bottom: 1px solid #EAEFF3;

  .page-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 48px;

    .page-header-left {
      display: flex;
      align-items: center;
      flex: 1;

      .page-header-title {
        h2 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
      }

      .page-header-extra {
        margin-left: 16px;
        display: flex;
        align-items: center;
      }
    }

    .page-header-right {
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .page-header {
    padding: 12px 16px;

    .page-header-content {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;

      .page-header-left,
      .page-header-right {
        flex: none;
      }

      .page-header-left {
        justify-content: flex-start;
      }

      .page-header-right {
        justify-content: center;
      }
    }
  }
}

@media (max-width: 480px) {
  .page-header {
    .page-header-content {
      .page-header-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .page-header-extra {
          margin-left: 0;
        }
      }
    }
  }
}
