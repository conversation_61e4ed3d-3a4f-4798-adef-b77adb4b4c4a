import React from 'react';
import PersonalCenter from '../PersonalCenter';
import './index.less';

interface PageHeaderProps {
  title?: string;
  extra?: React.ReactNode;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, extra, className = '' }) => {
  return (
    <div className={`page-header ${className}`}>
      <div className="page-header-content">
        <div className="page-header-left">
          {title && (
            <div className="page-header-title">
              <h2>{title}</h2>
            </div>
          )}
          {extra && (
            <div className="page-header-extra">
              {extra}
            </div>
          )}
        </div>
        <div className="page-header-right">
          <PersonalCenter />
        </div>
      </div>
    </div>
  );
};

export default PageHeader;
