import React from 'react';
import { Space, Tooltip, message } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';

const DataStats: React.FC = () => {
  const handleClick = () => {
    message.info('跳转到数据统计页面');
  };

  return (
    <Tooltip title="数据统计" placement="bottom">
      <div className="data-stats" onClick={handleClick}>
        <Space align="center" size={8}>
          <BarChartOutlined className="stats-icon" />
          <span className="stats-text">数据统计</span>
        </Space>
      </div>
    </Tooltip>
  );
};

export default DataStats;
