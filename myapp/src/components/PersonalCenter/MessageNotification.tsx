import React from 'react';
import { Badge, Tooltip, message } from 'antd';
import { BellOutlined } from '@ant-design/icons';

const MessageNotification: React.FC = () => {
  // 模拟未读消息数量，实际项目中应该从状态管理或API获取
  const unreadCount = 3;

  const handleClick = () => {
    message.info('跳转到消息中心');
  };

  return (
    <Tooltip title="消息" placement="bottom">
      <div className="message-notification" onClick={handleClick}>
        <Badge count={unreadCount} size="small">
          <BellOutlined className="message-icon" />
        </Badge>
        <span className="message-text">消息</span>
      </div>
    </Tooltip>
  );
};

export default MessageNotification;
