.personal-center {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 16px;

  .ant-divider-vertical {
    height: 20px;
    margin: 0 16px;
    border-color: #e8e8e8;
  }

  .data-stats {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      // background-color: rgba(0, 0, 0, 0.04);
    }

    .stats-icon {
      font-size: 18px;
      color: #666;
      margin-right: 6px;
    }

    .stats-text {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }

  .message-notification {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    gap: 6px;

    &:hover {
      // background-color: rgba(0, 0, 0, 0.04);
    }

    .message-icon {
      font-size: 18px;
      color: #666;
    }

    .message-text {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }

  .user-avatar-section {
    .user-info {
      cursor: pointer;
      padding: 6px 12px;
      border-radius: 6px;
      transition: all 0.3s ease;

      &:hover {
        // background-color: rgba(0, 0, 0, 0.04);
      }

      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #f0f0f0;
      }

      .user-name {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        margin-left: 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .personal-center {
    padding: 0 8px;

    .data-stats,
    .message-notification {
      padding: 6px 8px;

      .stats-text,
      .message-text {
        display: none; // 在小屏幕上隐藏文字，只显示图标
      }
    }

    .user-info {
      .user-name {
        display: none; // 在小屏幕上隐藏用户名
      }
    }
  }
}

@media (max-width: 480px) {
  .personal-center {
    .ant-space {
      gap: 8px !important;
    }
  }
}
