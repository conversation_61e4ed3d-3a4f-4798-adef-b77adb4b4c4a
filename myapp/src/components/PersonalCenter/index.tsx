import { RightOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Divider, Space } from 'antd';
import React from 'react';
import { AvatarDropdown } from '../RightContent/AvatarDropdown';
import DataStats from './DataStats';
import './index.less';
import MessageNotification from './MessageNotification';

const PersonalCenter: React.FC = () => {
  const { initialState } = useModel('@@initialState');

  return (
    <div className="personal-center mock-user-center">
      <Space size={0} align="center" split={<Divider type="vertical" />}>
        {/* 数据统计 */}
        <DataStats />

        {/* 消息通知 */}
        <MessageNotification />

        {/* 用户头像和下拉菜单 */}
        <div className="user-avatar-section mock-user-avatar-section">
          <AvatarDropdown menu>
            <Space align="center" className="user-info">
              <img
                src={
                  initialState?.currentUser?.avatar ||
                  'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
                }
                alt="avatar"
                className="user-avatar"
              />
              <span className="user-name">{initialState?.currentUser?.name || '王爱国'}</span>
              <RightOutlined style={{ color: '#666', fontSize: 16, marginLeft: 8 }} />
            </Space>
          </AvatarDropdown>
        </div>
      </Space>
    </div>
  );
};

export default PersonalCenter;
