.search-filter {
  // margin-bottom: 20px;

  .search-filter-row {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }

    &.search-filter-controls {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .search-filter-input {
    height: 40px !important;
    border-radius: 8px !important;
  }

  .search-filter-select .ant-select-selector {
    height: 40px !important;
    display: flex;
    align-items: center;
    border-radius: 8px !important;
    background: #fff;
    transition: border-color 0.2s, box-shadow 0.2s;
  }

  .search-filter-select .ant-select-selector:hover {
    border-color: #0AAC79 !important;
  }

  .search-filter-select.ant-select-focused .ant-select-selector,
  .search-filter-select .ant-select-selector:focus,
  .search-filter-select .ant-select-selector:focus-within {
    border-color: #0AAC79 !important;
    box-shadow: 0 0 0 2px rgba(10, 172, 121, 0.06) !important;
  }
} 