import React from 'react';
import { Input, Select, Button } from 'antd';
import './index.less';

interface SearchActionBarProps {
  searchKeyword: string;
  onKeywordChange: (val: string) => void;
  appStatus?: string;
  onStatusChange?: (val: string | undefined) => void;
  onQuery: () => void;
  onReset: () => void;
  onPressEnter: () => void;
  showStatus?: boolean;
  prefix?: React.ReactNode;
}

const SearchActionBar: React.FC<SearchActionBarProps> = ({
  searchKeyword,
  onKeywordChange,
  appStatus,
  onStatusChange,
  onQuery,
  onReset,
  onPressEnter,
  showStatus = false,
  prefix,
}) => {
  return (
    <div className="search-filter">
      <div className="search-filter-row search-filter-controls">
        <Input
          placeholder="请输入关键字"
          allowClear
          value={searchKeyword}
          onChange={(e) => onKeywordChange(e.target.value)}
          onPressEnter={onPressEnter}
          style={{ width: 302, height: 40 }}
          className="search-filter-input"
          prefix={prefix}
        />
        {showStatus && (
          <Select
            placeholder="应用状态"
            allowClear
            style={{ width: 120, height: 40 }}
            className="search-filter-select"
            value={appStatus}
            onChange={onStatusChange}
            options={[
              { value: '1', label: '草稿' },
              { value: '2', label: '已发布' },
            ]}
          />
        )}
        <Button
          color="default"
          variant="solid"
          onClick={onQuery}
          style={{ width: '56px', height: '40px', borderRadius: '8px' }}
        >
          查询
        </Button>
        <Button
          color="default"
          variant="filled"
          onClick={onReset}
          style={{ width: '56px', height: '40px', borderRadius: '8px' }}
        >
          重置
        </Button>
      </div>
    </div>
  );
};

export default SearchActionBar; 