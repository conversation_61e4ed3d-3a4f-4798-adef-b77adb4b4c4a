import { componentApi } from '@/services/api';
import { Avatar, Button, Col, Modal, Row, Spin, message } from 'antd';
import React, { useEffect, useState } from 'react';
import './index.less';
import { ModelCard } from '@/components';
import { PageContainer } from '@ant-design/pro-components';
import SearchActionBar from '@/components/SearchActionBar';

interface ComponentItem {
  id: string;
  name: string;
  description: string;
  icon?: string;
  isOnline: number;
}

const ComponentList: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(12);
  const [loading, setLoading] = useState<boolean>(false);
  const [componentList, setComponentList] = useState<ComponentItem[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentComponent, setCurrentComponent] = useState<ComponentItem | null>(null);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  const fetchComponentList = async () => {
    setLoading(true);
    try {
      const { data, total } = await componentApi.getComponentList({
        page: currentPage,
        pageSize,
        keywords: searchKeyword || undefined,
      });
      setComponentList(data);
      setTotal(total);
    } catch (error: any) {
      console.error('获取组件列表出错:', error);
      message.error('获取组件列表失败：' + (error.message || '未知错误'));
      setComponentList([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComponentList();
  }, [currentPage, pageSize, searchKeyword]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDetailClick = async (component: ComponentItem) => {
    try {
      const { result } = await componentApi.getComponentDetail(component.id);

      if (!result || typeof result !== 'object') {
        throw new Error('返回数据格式错误');
      }

      setCurrentComponent({
        id: result.id,
        name: result.name || component.name || '',
        description: result.description || component.description || '',
        isOnline: result.isOnline ?? component.isOnline ?? 0,
        ...(result.icon && { icon: result.icon }),
      });
      setDetailVisible(true);
    } catch (error: any) {
      message.error('获取详情失败：' + (error.message || '未知错误'));
    }
  };

  const handleDetailClose = () => {
    setDetailVisible(false);
    setCurrentComponent(null);
  };

  const handleStatusChange = async (id: string, checked: boolean) => {
    try {
      await componentApi.updateComponentStatus({
        id,
        isOnline: checked ? 1 : 0,
      });
      message.success(`状态更新成功：${checked ? '已上架' : '未上架'}`);
      setComponentList((prevList) =>
        prevList.map((item) => (item.id === id ? { ...item, isOnline: checked ? 1 : 0 } : item)),
      );
    } catch (error: any) {
      message.error('状态更新失败：' + (error.message || '未知错误'));
    }
  };

  return (
    <PageContainer>
      {/* <Breadcrumb
        items={[{ title: '组件' }, { title: '组件列表' }]}
        style={{ marginBottom: '16px' }}
      /> */}
      <SearchActionBar
        searchKeyword={searchKeyword}
        onKeywordChange={setSearchKeyword}
        onQuery={() => {
          setCurrentPage(1);
          fetchComponentList();
        }}
        onReset={() => {
          setSearchKeyword('');
          setCurrentPage(1);
        }}
        onPressEnter={() => {
          setCurrentPage(1);
          fetchComponentList();
        }}
        showStatus={false}
      />
      <Row gutter={[16, 16]} style={{marginTop:'20px'}}>
        {loading ? (
          <Col span={24}>
            <div style={{ padding: '50px', textAlign: 'center' }}>
              <Spin />
              <div style={{ marginTop: 16 }}>加载中...</div>
            </div>
          </Col>
        ) : (
          <>
            {componentList.map((component: ComponentItem, index: number) => (
              <Col xs={24} sm={24} md={12} lg={12} key={component.id || index}>
                <ModelCard
                  id={component.id}
                  name={component.name}
                  description={component.description}
                  icon={component.icon}
                  isOnline={component.isOnline}
                  onDetailClick={() => handleDetailClick(component)}
                  onStatusChange={(checked: boolean) => handleStatusChange(component.id, checked)}
                />
              </Col>
            ))}
          </>
        )}
      </Row>

      <Modal
        title="详情"
        open={detailVisible}
        onCancel={handleDetailClose}
        footer={[
          <Button key="close" onClick={handleDetailClose}>
            确定
          </Button>,
        ]}
        width={600}
      >
        {currentComponent && (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>名称：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {currentComponent.name}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>图标：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                <Avatar src={currentComponent.icon} size={48} />
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>描述：</div>
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  minHeight: '100px',
                }}
              >
                {currentComponent.description}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {total > 0 && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            &lt;
          </Button>
          {Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => (
            <Button
              key={i + 1}
              type={currentPage === i + 1 ? 'primary' : 'default'}
              style={{ margin: '0 8px' }}
              onClick={() => handlePageChange(i + 1)}
            >
              {i + 1}
            </Button>
          ))}
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === Math.ceil(total / pageSize)}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            &gt;
          </Button>
        </div>
      )}
    </PageContainer>
  );
};

export default ComponentList;
