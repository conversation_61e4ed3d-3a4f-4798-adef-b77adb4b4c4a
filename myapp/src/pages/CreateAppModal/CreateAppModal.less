.createAppModal {
  .create-app-modal-tabs-bar .custom-filter-segmented {
    margin-bottom: 16px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
  
    .ant-segmented {
      background: transparent;
      border: none;
      padding: 0;
      gap: 8px;
      min-width: 220px;
      height: 32px;
      box-shadow: none;
  
      .ant-segmented-group {
        width: 100%;
      }
  
      .ant-segmented-item {
        width: 50%;
        font-size: 14px;
        padding: 6px 16px;
        border-radius: 6px;
        background: #fff;
        border: 1px solid #e5e6eb;
        color: #666;
        font-weight: 400;
        height: 32px;
        line-height: 32px;
        min-width: auto;
        margin: 0;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
  
        &:hover {
          border-color: #0AAC79;
          color: #0AAC79;
        }
      }
  
      .ant-segmented-item-selected {
        background: #0AAC79;
        border-color: #0AAC79;
        color: #fff;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(10, 172, 121, 0.2);
        z-index: 1;
  
        &:hover {
          background: #0AAC79;
          border-color: #0AAC79;
          color: #fff;
        }
      }
  
      .ant-segmented-item:not(.ant-segmented-item-selected) {
        border: 1px solid #e5e6eb;
        color: #666;
        background: #fff;
        font-weight: 400;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
      }
  
      .ant-segmented-thumb {
        display: none;
      }
    }
  
    .segmented-label {
      display: inline-flex;
      align-items: baseline;
      gap: 8px;
      font-size: 16px;
      font-weight: 500;
      color: #939CAF;
      line-height: 1;
      .segmented-icon {
        width: 18px;
        height: 18px;
        display: inline-block;
        vertical-align: text-bottom;
        margin-bottom: -2px;
        margin-right: 4px;
        filter: grayscale(1) brightness(0.95);
      }
    }
  
    .ant-segmented-item-selected .segmented-label {
      color: #0AAC79;
      .segmented-icon {
        filter: none;
      }
    }
  
    .ant-segmented-item:not(.ant-segmented-item-selected) .segmented-label {
      color: #939CAF;
      .segmented-icon {
        filter: grayscale(1) brightness(0.95);
      }
    }
  } 
}

.createAppModal .ant-segmented-item .segmented-label .segmented-icon {
  width: 16px !important;
  height: 16px !important;
  vertical-align: text-bottom !important;
  margin-right: 6px !important;
  filter: grayscale(1) brightness(0.95) !important;
  transition: filter 0.2s, box-shadow 0.2s;
}
.createAppModal .ant-segmented-item-selected .segmented-label .segmented-icon {
  filter: none !important;
  color: #0AAC79 !important;
}

.createAppModal .ant-segmented-item .segmented-label {
  color: #939CAF !important;
}
.createAppModal .ant-segmented-item-selected .segmented-label {
  color: #0AAC79 !important;
}
