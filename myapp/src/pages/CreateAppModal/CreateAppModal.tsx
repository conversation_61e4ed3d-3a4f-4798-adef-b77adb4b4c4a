import { PlusOutlined } from '@ant-design/icons';
import { Form, Input, Modal, Upload, message, Segmented } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import React, { useState } from 'react';
import './CreateAppModal.less';

interface CreateAppModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
}

const CreateAppModal: React.FC<CreateAppModalProps> = ({ visible, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const formData = new FormData();

      // 添加基本字段
      formData.append('name', values.name);
      formData.append('description', values.description || '');
      formData.append('type', values.appType === '智能体' ? '1' : '2');

      // 添加文件
      if (fileList[0]?.originFileObj) {
        formData.append('file', fileList[0].originFileObj);
      }

      onOk(formData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件！');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB！');
      return false;
    }
    return true;
  };

  const handleChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  return (
    <Modal
      title="新增应用"
      open={visible}
      onCancel={() => {
        form.resetFields();
        setFileList([]);
        onCancel();
      }}
      onOk={handleSubmit}
      destroyOnHidden
      className='createAppModal'

    >
      <Form form={form} layout="vertical" initialValues={{ appType: '智能体' }} >
        <Form.Item
          name="appType"
          label={<span style={{ color: '#ff4d4f' }}>应用类型</span>}
          rules={[{ required: true, message: '请选择应用类型' }]}
        >
          <Segmented
            options={[
              {
                label: (
                  <span className="segmented-label">
                    <img src="http://192.168.200.22:9010/hj-rule-engine/static/agent_icon.svg" className="segmented-icon" alt="agent" />
                    智能体
                  </span>
                ),
                value: '智能体',
              },
              {
                label: (
                  <span className="segmented-label">
                    <img src="http://192.168.200.22:9010/hj-rule-engine/static/flow_icon.svg" className="segmented-icon" alt="flow" />
                    工作流
                  </span>
                ),
                value: '工作流',
              },
            ]}
            className="create-app-modal-tabs-bar custom-filter-segmented"
            block
            onChange={val => form.setFieldsValue({ appType: val })}
            value={form.getFieldValue('appType')}
          />
        </Form.Item>

        <Form.Item
          name="name"
          label={<span style={{ color: '#ff4d4f' }}>应用名称</span>}
          rules={[
            { required: true, message: '请输入应用名称' },
            { max: 30, message: '应用名称不能超过30字' },
          ]}
        >
          <Input placeholder="不超过30字" maxLength={30} />
        </Form.Item>

        <Form.Item
          name="description"
          label="应用描述"
          rules={[{ max: 100, message: '应用描述不能超过100字' }]}
        >
          <Input.TextArea placeholder="不超过100字" autoSize={{ minRows: 3, maxRows: 6 }} />
        </Form.Item>

        <Form.Item name="icon" label="应用图标">
          <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            maxCount={1}
          >
            {fileList.length === 0 && (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            )}
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateAppModal;
