import React, { useEffect, useState } from 'react';
import { Input, Tree, Dropdown, Modal, message, Segmented, MenuProps } from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';
import {
  fetchDataCategoryList,
  addDataCategory,
  updateDataCategory,
  preDeleteDataCategory,
  deleteDataCategoryById,
  fetchStructuredDataTableList,
  addStructuredDataTable,
  updateStructuredDataTable,
  deleteStructuredDataTable,
} from '@/services/api/dataCategory';

const rootCategoryKey = 'root';
const structuredRootKey = 'structured_root';

const transformApiDataToTree = (apiData: any) => {
  if (!apiData) return [];
  const transformNode = (node: any) => ({
    title: node.name,
    key: node.id,
    rawTitle: node.name,
    description: node.description,
    children: node.children?.map(transformNode) || [],
  });
  return [
    {
      title: apiData.name,
      key: apiData.id,
      rawTitle: apiData.name,
      description: apiData.description,
      children: apiData.children?.map(transformNode) || [],
    },
  ];
};

const withRawTitle = (data: any[]): any[] =>
  data.map((item) => ({
    ...item,
    rawTitle: item.title,
    children: item.children ? withRawTitle(item.children) : undefined,
  }));

const DataSider = ({
  onCategoryChange,
  onTabChange,
  defaultTab = 'unstructured',
  selectedKeys: controlledSelectedKeys,
  onSelectedKeysChange,
  expandedKeys: controlledExpandedKeys,
  onExpandedKeysChange,
  onTreeDataChange,
}: {
  onCategoryChange: (categoryId: string, categoryName: string, description: string) => void;
  onTabChange: (tab: 'unstructured' | 'structured') => void;
  defaultTab?: string;
  selectedKeys?: React.Key[];
  onSelectedKeysChange?: (keys: React.Key[]) => void;
  expandedKeys?: React.Key[];
  onExpandedKeysChange?: (keys: React.Key[]) => void;
  onTreeDataChange?: (tree: any[]) => void;
}) => {
  const [treeData, setTreeData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([rootCategoryKey]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [categoryKeyword, setCategoryKeyword] = useState('');
  const [activeTab, setActiveTab] = useState(defaultTab);
  const [currentNode, setCurrentNode] = useState<any>(null);
  const [subModalVisible, setSubModalVisible] = useState(false);
  const [subCategoryName, setSubCategoryName] = useState('');
  const [subCategoryDesc, setSubCategoryDesc] = useState('');
  const [parentCategory, setParentCategory] = useState('');
  const [categoryDeleteModalVisible, setCategoryDeleteModalVisible] = useState(false);
  const [currentDeleteCategory, setCurrentDeleteCategory] = useState<any>(null);
  const [categoryDeleteErrorMessage, setCategoryDeleteErrorMessage] = useState('');
  // 这两个变量用于onCategoryChange参数，主页面依赖目录名和描述的同步，不能删除
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedCategory, setSelectedCategory] = useState('');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [selectedDescription, setSelectedDescription] = useState('');

  /**
   * 获取非结构化数据目录树
   */
  const fetchTree = async () => {
    setLoading(true);
    try {
      const data = await fetchDataCategoryList();
      if (data.success) {
        const tree = withRawTitle(transformApiDataToTree(data.result));
        setTreeData(tree);
        if (onTreeDataChange) onTreeDataChange(tree);
      } else {
        message.error('获取数据目录失败');
      }
    } catch {
      message.error('请求数据目录出错');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取结构化数据表树
   */
  const fetchStructuredTree = async () => {
    setLoading(true);
    try {
      const data = await fetchStructuredDataTableList();
      if (data.success) {
        const children = (data.result || []).map((item: any) => ({
          title: item.tableName,
          key: item.tableId,
          rawTitle: item.tableName,
          description: '',
          children: [],
        }));
        const tree = [
          {
            title: '所有数据表',
            key: structuredRootKey,
            rawTitle: '所有数据表',
            description: '',
            children,
          },
        ];
        setTreeData(tree);
        if (onTreeDataChange) onTreeDataChange(tree);
      } else {
        message.error('获取结构化数据表失败');
      }
    } catch {
      message.error('请求结构化数据表出错');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (activeTab === 'unstructured') {
      fetchTree();
    } else {
      fetchStructuredTree();
    }
    // eslint-disable-next-line
  }, []);

  // 搜索过滤
  const searchInTree = (nodes: any[], keyword: string): any[] => {
    if (!keyword) return nodes;
    const lowerKeyword = keyword.toLowerCase();
    return nodes
      .map((node) => {
        const titleMatch = node.title.toLowerCase().includes(lowerKeyword);
        let matchedChildren: any[] = [];
        if (node.children) {
          matchedChildren = searchInTree(node.children, keyword);
        }
        const hasMatchingDescendants = (nodeList: any[]): boolean => {
          return nodeList.some((child) => {
            const childMatches = child.title.toLowerCase().includes(lowerKeyword);
            const grandChildMatches = child.children
              ? hasMatchingDescendants(child.children)
              : false;
            return childMatches || grandChildMatches;
          });
        };
        const hasDescendantMatch = node.children ? hasMatchingDescendants(node.children) : false;
        if (titleMatch || matchedChildren.length > 0 || hasDescendantMatch) {
          return {
            ...node,
            children: matchedChildren.length > 0 ? matchedChildren : node.children,
          };
        }
        return null;
      })
      .filter(Boolean);
  };

  const getMatchedKeys = (nodes: any[], keyword: string): string[] => {
    if (!keyword) return [];
    const keys: string[] = [];
    const lowerKeyword = keyword.toLowerCase();
    const traverse = (nodeList: any[], parentKeys: string[] = []) => {
      nodeList.forEach((node) => {
        const currentPath = [...parentKeys, node.key];
        const nodeMatches = node.title.toLowerCase().includes(lowerKeyword);
        const hasMatchingDescendants = (children: any[]): boolean => {
          return children.some((child) => {
            const childMatches = child.title.toLowerCase().includes(lowerKeyword);
            const grandChildMatches = child.children
              ? hasMatchingDescendants(child.children)
              : false;
            return childMatches || grandChildMatches;
          });
        };
        const hasDescendantMatch = node.children ? hasMatchingDescendants(node.children) : false;
        if (nodeMatches || hasDescendantMatch) {
          parentKeys.forEach((parentKey) => {
            if (!keys.includes(parentKey)) {
              keys.push(parentKey);
            }
          });
          if (node.children && hasDescendantMatch) {
            if (!keys.includes(node.key)) {
              keys.push(node.key);
            }
          }
        }
        if (node.children) {
          traverse(node.children, currentPath);
        }
      });
    };
    traverse(nodes);
    return [...new Set(keys)];
  };

  const filteredTreeData = searchInTree(treeData, categoryKeyword);

  // 搜索关键词变化时更新展开状态
  useEffect(() => {
    if (categoryKeyword) {
      const matchedKeys = getMatchedKeys(treeData, categoryKeyword);
      if (onExpandedKeysChange) {
        onExpandedKeysChange(matchedKeys);
      } else {
        setExpandedKeys(matchedKeys);
      }
    }
  }, [categoryKeyword, treeData]);

  // 目录节点点击
  const handleTreeSelect = (keys: React.Key[], info: any) => {
    setSelectedCategory(info.node.rawTitle || '');
    setSelectedDescription(info.node.description || '');
    if (onSelectedKeysChange) {
      onSelectedKeysChange([info.node.key]);
    } else {
      setSelectedKeys([info.node.key]);
    }
    onCategoryChange(info.node.key, info.node.rawTitle, info.node.description);
    if (info.node.children && info.node.children.length > 0) {
      let newExpandedKeys;
      if ((controlledExpandedKeys ?? expandedKeys).includes(info.node.key)) {
        newExpandedKeys = (controlledExpandedKeys ?? expandedKeys).filter(
          (key) => key !== info.node.key,
        );
      } else {
        newExpandedKeys = [...(controlledExpandedKeys ?? expandedKeys), info.node.key];
      }
      if (onExpandedKeysChange) {
        onExpandedKeysChange(newExpandedKeys);
      } else {
        setExpandedKeys(newExpandedKeys);
      }
    }
  };

  /**
   * Tab切换处理函数
   * 在Tab切换时强制重置到根目录，确保每次切换都从根目录开始
   * @param val - 新的Tab值
   */
  const handleTabChange = (val: 'unstructured' | 'structured') => {
    setActiveTab(val);
    onTabChange(val);
    // 清空搜索关键词
    setCategoryKeyword('');
    // 切换tab时先清空树数据，避免状态冲突
    setTreeData([]);

    // Tab切换时强制重置状态到根目录，不管是否有父组件控制
    // 这样确保每次Tab切换都从根目录开始
    if (val === 'unstructured') {
      // 先清空状态，等数据加载完成后会自动设置根目录
      if (onSelectedKeysChange) {
        onSelectedKeysChange([]);
      } else {
        setSelectedKeys([]);
      }
      if (onExpandedKeysChange) {
        onExpandedKeysChange([]);
      } else {
        setExpandedKeys([]);
      }
      fetchTree();
    } else {
      // 结构化数据直接设置为结构化根目录
      if (onSelectedKeysChange) {
        onSelectedKeysChange([structuredRootKey]);
      } else {
        setSelectedKeys([structuredRootKey]);
      }
      if (onExpandedKeysChange) {
        onExpandedKeysChange([structuredRootKey]);
      } else {
        setExpandedKeys([structuredRootKey]);
      }
      setSelectedCategory('所有数据表');
      setSelectedDescription('');
      onCategoryChange(structuredRootKey, '所有数据表', '');
      fetchStructuredTree();
    }
  };

  /**
   * 监听treeData变化，数据加载完成后设置默认选中和展开状态
   * 区分Tab切换和从详情页返回两种场景
   */
  useEffect(() => {
    if (treeData.length > 0) {
      // 检查是否有父组件控制的状态（从详情页返回时会有）
      const hasControlledSelection = controlledSelectedKeys && controlledSelectedKeys.length > 0;
      const hasControlledExpansion = controlledExpandedKeys && controlledExpandedKeys.length > 0;

      // 检查当前状态是否为空（Tab切换后会清空状态）
      const currentSelectedKeys = controlledSelectedKeys || selectedKeys;
      const currentExpandedKeys = controlledExpandedKeys || expandedKeys;
      const isStateEmpty =
        (!currentSelectedKeys || currentSelectedKeys.length === 0) &&
        (!currentExpandedKeys || currentExpandedKeys.length === 0);

      if (activeTab === 'unstructured') {
        const rootNode = treeData[0];
        const rootKey = rootNode.key;

        // 如果状态为空（Tab切换场景）或没有外部控制状态，则设置根目录
        if (isStateEmpty || !hasControlledSelection) {
          if (onSelectedKeysChange) {
            onSelectedKeysChange([rootKey]);
          } else {
            setSelectedKeys([rootKey]);
          }
          setSelectedCategory(rootNode.rawTitle || '');
          setSelectedDescription(rootNode.description || '');
          onCategoryChange(rootKey, rootNode.rawTitle || '', rootNode.description || '');
        }

        if (isStateEmpty || !hasControlledExpansion) {
          if (onExpandedKeysChange) {
            onExpandedKeysChange([rootKey]);
          } else {
            setExpandedKeys([rootKey]);
          }
        }
      } else if (activeTab === 'structured') {
        // 结构化tab下，若有数据表则默认选中第一个数据表
        const rootNode = treeData[0];
        const rootKey = rootNode.key;
        const firstTable = rootNode.children && rootNode.children.length > 0 ? rootNode.children[0] : null;
        if (firstTable) {
          if (onSelectedKeysChange) {
            onSelectedKeysChange([firstTable.key]);
          } else {
            setSelectedKeys([firstTable.key]);
          }
          setSelectedCategory(firstTable.rawTitle || '');
          setSelectedDescription(firstTable.description || '');
          onCategoryChange(firstTable.key, firstTable.rawTitle || '', firstTable.description || '');
          // 展开根节点
          if (onExpandedKeysChange) {
            onExpandedKeysChange([rootKey]);
          } else {
            setExpandedKeys([rootKey]);
          }
        } else {
          // 没有数据表时，选中根节点
          if (onSelectedKeysChange) {
            onSelectedKeysChange([rootKey]);
          } else {
            setSelectedKeys([rootKey]);
          }
          setSelectedCategory(rootNode.rawTitle || '');
          setSelectedDescription(rootNode.description || '');
          onCategoryChange(rootKey, rootNode.rawTitle || '', rootNode.description || '');
          if (onExpandedKeysChange) {
            onExpandedKeysChange([rootKey]);
          } else {
            setExpandedKeys([rootKey]);
          }
        }
      }
      // 结构化数据的状态已在Tab切换时直接设置，这里不需要额外处理
    }
    // eslint-disable-next-line
  }, [treeData, activeTab]);

  // 分类删除检查（提前声明，避免使用前报错）
  const handleCategoryDeleteCheck = async (node: any) => {
    try {
      setCurrentDeleteCategory(node);
      setCategoryDeleteErrorMessage('');

      // 结构化数据表删除时，跳过preDelete检查，直接显示删除确认弹框
      if (activeTab === 'structured') {
        setCategoryDeleteModalVisible(true);
        return;
      }

      // 非结构化数据分类删除时，需要进行preDelete检查
      const preDeleteRes = await preDeleteDataCategory(node.key);
      if (!preDeleteRes.success) {
        setCategoryDeleteErrorMessage(preDeleteRes.message || '预删除检查失败');
        setCategoryDeleteModalVisible(true);
        return;
      }
      const { canDelete, hasFiles, hasChildrenCate } = preDeleteRes.result;
      if (!canDelete) {
        if (hasFiles) {
          setCategoryDeleteErrorMessage('此分类下已有数据 无法删除');
        } else if (hasChildrenCate) {
          setCategoryDeleteErrorMessage('此分类下存在子分类 无法删除');
        } else {
          setCategoryDeleteErrorMessage('当前分类不可删除');
        }
      }
      setCategoryDeleteModalVisible(true);
    } catch {
      setCategoryDeleteErrorMessage('删除检查请求失败');
      setCategoryDeleteModalVisible(true);
    }
  };

  // 菜单操作
  const handleMenuClick: MenuProps['onClick'] = async ({ key }) => {
    if (key === 'add') {
      setCurrentNode({ ...currentNode, menuAction: 'add' });
      setParentCategory(currentNode?.title || '');
      setSubModalVisible(true);
    } else if (key === 'edit') {
      setCurrentNode({ ...currentNode, menuAction: 'edit' });
      setParentCategory(currentNode?.title || '');
      setSubCategoryName(currentNode?.rawTitle || '');
      setSubCategoryDesc(currentNode?.description || '');
      setSubModalVisible(true);
    } else if (key === 'delete') {
      handleCategoryDeleteCheck(currentNode);
    }
  };

  // 新增/编辑分类
  const handleAddSubCategory = async () => {
    if (!subCategoryName) {
      message.warning(activeTab === 'structured' ? '请输入表名称' : '请输入类型名称');
      return;
    }
    try {
      let result;
      // 结构化数据表的处理
      if (activeTab === 'structured') {
        if (currentNode?.menuAction === 'add') {
          // 新增数据表
          result = await addStructuredDataTable({
            tableName: subCategoryName,
            tableDesc: subCategoryDesc,
          });
        } else {
          // 编辑数据表
          result = await updateStructuredDataTable({
            tableId: currentNode.key,
            tableName: subCategoryName,
            tableDesc: subCategoryDesc,
          });
        }
        if (result.success) {
          message.success(currentNode?.menuAction === 'add' ? '添加成功' : '更新成功');
          fetchStructuredTree();
        } else {
          message.error(result.message || '操作失败');
        }
      } else {
        // 非结构化数据分类的处理
        if (currentNode?.key === rootCategoryKey || !currentNode) {
          result = await addDataCategory({
            currentId: rootCategoryKey,
            categoryName: subCategoryName,
            description: subCategoryDesc,
          });
        } else if (currentNode?.menuAction === 'add') {
          result = await addDataCategory({
            currentId: currentNode.key,
            categoryName: subCategoryName,
            description: subCategoryDesc,
          });
        } else {
          result = await updateDataCategory({
            id: currentNode.key,
            categoryName: subCategoryName,
            description: subCategoryDesc,
          });
        }
        if (result.success) {
          message.success(currentNode?.key === rootCategoryKey ? '添加成功' : '更新成功');
          fetchTree();
        } else {
          message.error(result.message || '操作失败');
        }
      }
    } catch {
      message.error('请求失败');
    } finally {
      setSubModalVisible(false);
      setSubCategoryName('');
      setSubCategoryDesc('');
    }
  };

  /**
   * 删除分类/数据表确认处理函数
   * 根据当前Tab类型调用不同的删除接口
   */
  const handleCategoryDeleteConfirm = async () => {
    if (categoryDeleteErrorMessage) return;
    try {
      let deleteRes;
      // 结构化数据表的删除
      if (activeTab === 'structured') {
        deleteRes = await deleteStructuredDataTable(currentDeleteCategory.key);
        if (deleteRes.success) {
          message.success('删除成功');
          fetchStructuredTree();
        } else {
          setCategoryDeleteErrorMessage(deleteRes.message || '删除失败');
        }
      } else {
        // 非结构化数据分类的删除
        deleteRes = await deleteDataCategoryById(currentDeleteCategory.key);
        if (deleteRes.success) {
          message.success('删除成功');
          fetchTree();
        } else {
          setCategoryDeleteErrorMessage(deleteRes.message || '删除失败');
        }
      }

      if (deleteRes.success) {
        setCategoryDeleteModalVisible(false);
        setCurrentDeleteCategory(null);
        setCategoryDeleteErrorMessage('');
      }
    } catch {
      setCategoryDeleteErrorMessage('删除请求失败');
    }
  };

  // 高亮关键词
  const highlightKeyword = (text: string, keyword: string) => {
    if (!keyword) return text;
    const regex = new RegExp(`(${keyword})`, 'gi');
    const parts = text.split(regex);
    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} style={{ backgroundColor: '#ffeb3b', color: '#000' }}>
          {part}
        </span>
      ) : (
        part
      ),
    );
  };

  // 节点标题
  const TreeNodeTitle: React.FC<{
    nodeData: any;
    categoryKeyword: string;
    getMenuItems: (node: any) => MenuProps['items'];
    handleMenuClick: MenuProps['onClick'];
    setCurrentNode: (node: any) => void;
  }> = ({ nodeData, categoryKeyword, getMenuItems, handleMenuClick, setCurrentNode }) => {
    const [showEllipsis, setShowEllipsis] = useState(false);
    // 判断是否为结构化tab下的数据表子节点
    const isStructuredTable = activeTab === 'structured' && nodeData.key !== structuredRootKey;
    return (
      <div
        className="data-tree-node-title"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          minHeight: 32,
          boxSizing: 'border-box',
          paddingRight: 8,
        }}
        onMouseEnter={() => setShowEllipsis(true)}
        onMouseLeave={() => setShowEllipsis(false)}
      >
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={isStructuredTable ? '/icons/structuredIcon.svg' : '/icons/file.svg'}
            alt="icon"
            style={{ width: 18, height: 18, marginRight: 6 }}
          />
          <span>{highlightKeyword(nodeData.title, categoryKeyword)}</span>
        </span>
        {showEllipsis && (
          <Dropdown
            menu={{ items: getMenuItems(nodeData), onClick: handleMenuClick }}
            trigger={['hover']}
            placement="bottomRight"
            onOpenChange={(open) => {
              if (open) setCurrentNode(nodeData);
            }}
          >
            <span onMouseDown={(e) => e.stopPropagation()}>
              <EllipsisOutlined className="data-tree-node-ellipsis" />
            </span>
          </Dropdown>
        )}
      </div>
    );
  };

  // 获取菜单项（根据tab和节点类型）
  const getMenuItems = (node: any): MenuProps['items'] => {
    // 结构化tab下菜单逻辑
    if (activeTab === 'structured') {
      if (node.key === structuredRootKey) {
        return [{ label: '新增数据表', key: 'add' }];
      }
      // 子节点（数据表）
      return [
        { label: '编辑', key: 'edit' },
        { label: '删除', key: 'delete' },
      ];
    }
    // 非结构化tab下菜单逻辑
    if (node.key === rootCategoryKey) {
      return [{ label: '新增子分类', key: 'add' }];
    }
    return [
      { label: '新增子分类', key: 'add' },
      { label: '编辑', key: 'edit' },
      { label: '删除', key: 'delete' },
    ];
  };

  // 渲染树节点
  const renderTitle = (nodeData: any) => (
    <TreeNodeTitle
      nodeData={nodeData}
      categoryKeyword={categoryKeyword}
      getMenuItems={getMenuItems}
      handleMenuClick={handleMenuClick}
      setCurrentNode={setCurrentNode}
    />
  );

  // 递归映射树形数据
  const mapTreeDataWithHighlight = (nodes: any[]): any[] => {
    return nodes.map((node) => ({
      ...node,
      title: renderTitle(node),
      children: node.children ? mapTreeDataWithHighlight(node.children) : undefined,
    }));
  };

  return (
    <div className="data-page-sider">
      <Segmented
        value={activeTab as 'unstructured' | 'structured'}
        onChange={handleTabChange}
        options={[
          { label: '非结构化数据', value: 'unstructured' },
          { label: '结构化数据', value: 'structured' },
        ]}
        className="custom-filter-segmented data-page-tabs-bar"
        size="middle"
      />
      <div className="data-page-sider-search-bar">
        <Input
          placeholder="请输入类目名称"
          allowClear
          value={categoryKeyword}
          onChange={(e) => setCategoryKeyword(e.target.value)}
          size="small"
          prefix={
            <img
              src="/icons/search.svg"
              alt="search"
              style={{ width: 18, height: 18, marginRight: 4 }}
            />
          }
        />
      </div>
      <div className="data-page-sider-tree-wrapper">
        {loading ? (
          <div style={{ padding: 16, textAlign: 'center' }}>加载中...</div>
        ) : (
          <Tree
            blockNode
            expandedKeys={controlledExpandedKeys ?? expandedKeys}
            onExpand={onExpandedKeysChange ?? setExpandedKeys}
            selectedKeys={controlledSelectedKeys ?? selectedKeys}
            treeData={mapTreeDataWithHighlight(filteredTreeData)}
            onSelect={handleTreeSelect}
            className="data-page-sider-tree"
          />
        )}
      </div>
      {/* 新增/编辑分类弹窗 */}
      <Modal
        title={
          activeTab === 'structured'
            ? currentNode?.menuAction === 'edit'
              ? '编辑数据表'
              : '新增数据表'
            : currentNode?.menuAction === 'edit'
            ? '编辑分类'
            : '新增子分类'
        }
        open={subModalVisible}
        onOk={handleAddSubCategory}
        onCancel={() => setSubModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        {activeTab === 'unstructured' && (
          <>
            <div className="modal-form-label">父级目录</div>
            <Input value={parentCategory} disabled />
          </>
        )}
        <div className="modal-form-label">
          <span className="required">*</span> {activeTab === 'structured' ? '表名称' : '类型名称'}
        </div>
        <Input
          maxLength={15}
          value={subCategoryName}
          onChange={(e) => setSubCategoryName(e.target.value)}
          placeholder="请输入"
          suffix={<span>{subCategoryName.length}/15</span>}
        />
        <div className="modal-form-label">{activeTab === 'structured' ? '表描述' : '类型描述'}</div>
        <Input.TextArea
          maxLength={100}
          value={subCategoryDesc}
          onChange={(e) => setSubCategoryDesc(e.target.value)}
          placeholder="请输入"
          rows={4}
          style={{ resize: 'none' }}
        />
        <div className="modal-form-count">{subCategoryDesc.length}/100</div>
      </Modal>
      {/* 删除分类弹窗 */}
      <Modal
        open={categoryDeleteModalVisible}
        onCancel={() => {
          setCategoryDeleteModalVisible(false);
          setCategoryDeleteErrorMessage('');
          setCurrentDeleteCategory(null);
        }}
        onOk={handleCategoryDeleteConfirm}
        okText="确定"
        cancelText="取消"
        title={activeTab === 'structured' ? '删除数据表' : '删除分类'}
      >
        {categoryDeleteErrorMessage ? (
          <div style={{ color: 'red', marginBottom: 8 }}>{categoryDeleteErrorMessage}</div>
        ) : (
          <div>{activeTab === 'structured' ? '确定要删除该数据表吗？' : '确定要删除该分类吗？'}</div>
        )}
      </Modal>
    </div>
  );
};

export default DataSider;
