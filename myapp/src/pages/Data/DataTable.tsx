import React, { useEffect, useState } from 'react';
import { Table, Tag, Select, Pagination, message, Modal } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  fetchUnstructuredDataList,
  batchDeleteUnstructuredData,
  fetchMineruDetail,
  deleteStructuredDataTable,
  fetchStructuredDataPageFieldConfig,
  fetchStructuredDataPage,
} from '@/services/api/dataCategory';
import dayjs from 'dayjs';

const statusColor = {
  已解析: 'green',
  解析中: 'orange',
  解析失败: 'red',
};

const getFileTypeIcon = (fileName: string) => {
  if (!fileName || typeof fileName !== 'string') return '/icons/file.svg';
  const extension = fileName.toLowerCase().split('.').pop();
  switch (extension) {
    case 'pdf':
      return '/icons/pdf.svg';
    case 'xls':
    case 'xlsx':
      return '/icons/excel.svg';
    case 'doc':
    case 'docx':
      return '/icons/word.svg';
    case 'ppt':
    case 'pptx':
      return '/icons/ppt.svg';
    default:
      return '/icons/file.svg';
  }
};

const truncateFileName = (fileName: string, maxLength: number = 30) => {
  if (!fileName || typeof fileName !== 'string') return '';
  if (fileName.length <= maxLength) {
    return fileName;
  }
  const start = Math.floor((maxLength - 3) / 2);
  const end = fileName.length - (maxLength - 3 - start);
  return fileName.substring(0, start) + '...' + fileName.substring(end);
};

interface DataTableProps {
  categoryId: string;
  searchKeyword: string;
  onSelectedRowKeysChange?: (keys: React.Key[]) => void;
  refreshKey?: number;
  activeTab?: 'unstructured' | 'structured';
  onShowPDFDetail?: (files: any[], file: any) => void;
  onLatestImportDateChange?: (date: string) => void;
}

const DataTable: React.FC<DataTableProps> = ({
  categoryId,
  searchKeyword,
  onSelectedRowKeysChange,
  refreshKey,
  activeTab = 'unstructured',
  onShowPDFDetail,
  onLatestImportDateChange,
}) => {
  const [tableData, setTableData] = useState<any[]>([]);
  const [tableLoading, setTableLoading] = useState(false);
  const [dataTotal, setDataTotal] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [current, setCurrent] = useState(1);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [deleteType, setDeleteType] = useState<'single' | 'batch'>('single');
  const [currentDeleteId, setCurrentDeleteId] = useState<string>('');
  const [structuredColumns, setStructuredColumns] = useState<ColumnsType<any>>([]);

  const pageCount = Math.ceil(dataTotal / pageSize);

  // 数据加载
  const loadTableData = async (
    categoryId: string,
    keywords = '',
    page = 1,
    pageSize: number = 10,
  ) => {
    setTableLoading(true);
    try {
      if (activeTab === 'structured') {
        if (categoryId === 'structured_root') {
          setTableData([]);
          setDataTotal(0);
          setStructuredColumns([]);
          setTableLoading(false);
          return;
        }
        // 先获取字段配置
        const fieldRes = await fetchStructuredDataPageFieldConfig({ tableId: categoryId });
        if (fieldRes.success && fieldRes.result.fieldConfig) {
          // 动态生成columns
          const dynamicColumns: ColumnsType<any> = Object.entries(fieldRes.result.fieldConfig).map(
            ([dataIndex, title]) => ({
              title: title as string,
              dataIndex,
              key: dataIndex,
              align: 'center',
              render: (text: any) => <span title={text}>{text}</span>,
            }),
          );
          setStructuredColumns(dynamicColumns);
          // 设置最新导入时间
          if (fieldRes.result.latestImportDate) {
            onLatestImportDateChange?.(fieldRes.result.latestImportDate);
          }
        } else {
          setStructuredColumns([]);
          onLatestImportDateChange?.('');
        }
        // 再获取数据
        const res = await fetchStructuredDataPage({
          tableId: categoryId,
          page,
          pageSize,
          keywords,
        });
        if (res.success && res.result) {
          setTableData((res.result.records || []).map((item: any) => ({ ...item, key: item.id })));
          setDataTotal(res.result.total || 0);
        } else {
          setTableData([]);
          setDataTotal(0);
        }
      } else {
        // 非结构化数据
        const res = await fetchUnstructuredDataList({
          categoryId,
          keywords,
          page,
          pageSize,
        });
        if (res.success) {
          setTableData(
            res.result.records.map((item: any) => ({
              ...item,
              key: item.id,
              name: item.originalFileName,
              status: item.statusCnDesc || item.status,
              size: item.fileSizeDesc,
              time: item.createTime,
            })),
          );
          setDataTotal(res.result.total);
        } else {
          message.error(res.message || '获取数据失败');
        }
      }
    } catch (error) {
      message.error('请求失败');
    } finally {
      setTableLoading(false);
    }
  };

  // 监听参数变化自动加载数据
  useEffect(() => {
    if (categoryId || activeTab === 'structured') {
      setCurrent(1);
      loadTableData(categoryId, searchKeyword, 1, pageSize);
    }
    // eslint-disable-next-line
  }, [categoryId, searchKeyword, pageSize, activeTab]);

  useEffect(() => {
    if (categoryId || activeTab === 'structured') {
      loadTableData(categoryId, searchKeyword, current, pageSize);
    }
    // eslint-disable-next-line
  }, [current]);

  // 新增：监听refreshKey变化强制刷新
  useEffect(() => {
    if (categoryId || activeTab === 'structured') {
      loadTableData(categoryId, searchKeyword, current, pageSize);
    }
    // eslint-disable-next-line
  }, [refreshKey]);

  // 删除
  const handleSingleDelete = (id: string) => {
    setCurrentDeleteId(id);
    setDeleteType('single');
    setDeleteModalVisible(true);
  };

  // 删除确认
  const handleDeleteConfirm = async () => {
    try {
      let result;
      if (activeTab === 'structured') {
        // 结构化数据删除
        if (deleteType === 'single') {
          result = await deleteStructuredDataTable(currentDeleteId);
        } else {
          // 批量删除结构化数据表
          const deletePromises = (selectedRowKeys as string[]).map((id) =>
            deleteStructuredDataTable(id),
          );
          const results = await Promise.all(deletePromises);
          result = { success: results.every((r) => r.success) };
        }
      } else {
        // 非结构化数据删除
        if (deleteType === 'single') {
          result = await batchDeleteUnstructuredData([currentDeleteId]);
        } else {
          result = await batchDeleteUnstructuredData(selectedRowKeys as string[]);
        }
      }

      if (result.success) {
        message.success(deleteType === 'single' ? '删除成功' : '批量删除成功');
        if (deleteType === 'batch') {
          setSelectedRowKeys([]);
        }
        // 刷新表格数据
        loadTableData(categoryId, searchKeyword, current, pageSize);
      } else {
        message.error(result.message || (deleteType === 'single' ? '删除失败' : '批量删除失败'));
      }
    } catch (error) {
      message.error(deleteType === 'single' ? '删除请求失败' : '批量删除请求失败');
    } finally {
      setDeleteModalVisible(false);
      setCurrentDeleteId('');
    }
  };

  const handleShowDetail = async (files: any[], file: any) => {
    try {
      const res = await fetchMineruDetail(file.key);
      if (res && res.success) {
        onShowPDFDetail?.(
          files.map((f) =>
            f.key === file.key
              ? {
                  ...f,
                  middelFileUrl: res.result.middelFileUrl,
                  originFileUrl: res.result.originFileUrl,
                }
              : f,
          ),
          {
            ...file,
            middelFileUrl: res.result.middelFileUrl,
            originFileUrl: res.result.originFileUrl,
          },
        );
      } else {
        message.error(res?.message || '获取详情失败');
      }
    } catch (e) {
      // message.error('请求详情失败');
    }
  };

  // 非结构化数据表格列
  const unstructuredColumns: ColumnsType<{
    key: string;
    name: string;
    status: string;
    size: string;
    time: string;
  }> = [
    {
      title: '序号',
      dataIndex: 'key',
      width: 60,
      align: 'center',
      render: (text: string, _: any, index: number) => (current - 1) * pageSize + index + 1,
    },
    {
      title: '文件名称',
      dataIndex: 'name',
      width: 260,
      render: (text: string) => (
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={getFileTypeIcon(text)}
            alt="file type"
            style={{ width: 22, height: 22, marginRight: 8 }}
          />
          <span
            title={text}
            style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
          >
            {truncateFileName(text)}
          </span>
        </span>
      ),
    },
    {
      title: '解析状态',
      dataIndex: 'status',
      width: 100,
      align: 'center',
      render: (status: string) =>
        status === '已解析' || status === '解析完成' ? (
          <Tag className="custom-status-parsed">{status}</Tag>
        ) : status === '解析中' ? (
          <Tag className="custom-status-parsing">{status}</Tag>
        ) : status === '解析失败' ? (
          <Tag className="custom-status-failed">{status}</Tag>
        ) : (
          <Tag color={statusColor[status as keyof typeof statusColor]}>{status}</Tag>
        ),
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      width: 100,
      align: 'center',
    },
    {
      title: '导入时间',
      dataIndex: 'time',
      width: 180,
      align: 'center',
      render: (time: string) => (time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <>
          <a
            style={{ marginRight: 6, color: '#0E77D1', cursor: 'pointer' }}
            onClick={() => handleShowDetail(tableData, record)}
          >
            详情
          </a>
          <a style={{ color: 'red' }} onClick={() => handleSingleDelete(record.key)}>
            删除
          </a>
        </>
      ),
    },
  ];

  // 根据activeTab动态选择表格列
  const columns = activeTab === 'structured' ? structuredColumns : unstructuredColumns;

  // 判断是否显示表格
  const shouldShowTable = () => {
    if (activeTab === 'structured') {
      // 结构化数据：只有在点击具体表目录时才显示表格
      return categoryId && categoryId !== 'structured_root';
    }
    // 非结构化数据：有categoryId就显示
    return !!categoryId;
  };

  return (
    <>
      <div className="data-page-content-table-wrapper">
        {shouldShowTable() ? (
          <Table
            columns={columns}
            dataSource={tableData}
            bordered
            size="middle"
            rowSelection={
              activeTab === 'structured'
                ? undefined
                : {
                    selectedRowKeys,
                    onChange: (keys) => {
                      setSelectedRowKeys(keys);
                      onSelectedRowKeysChange?.(keys);
                    },
                    preserveSelectedRowKeys: false,
                  }
            }
            className="data-page-content-table"
            pagination={false}
            loading={tableLoading}
            footer={() => (
              <div className="custom-pagination-bar">
                <span className="custom-pagination-total">共{dataTotal}条</span>
                <Select
                  value={pageSize}
                  onChange={(size) => {
                    setPageSize(size);
                    setCurrent(1);
                  }}
                  style={{ width: 90, margin: '0 8px' }}
                  options={[
                    { value: 10, label: '10条/页' },
                    { value: 20, label: '20条/页' },
                    { value: 50, label: '50条/页' },
                    { value: 100, label: '100条/页' },
                  ]}
                />
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <button
                    type="button"
                    className="custom-pagination-btn"
                    disabled={current === 1}
                    onClick={() => setCurrent(1)}
                  >
                    首页
                  </button>
                  <Pagination
                    current={current}
                    pageSize={pageSize}
                    total={dataTotal}
                    onChange={setCurrent}
                    showSizeChanger={false}
                    itemRender={(page, type, originalElement) => {
                      if (type === 'page') return originalElement;
                      if (type === 'prev') return originalElement;
                      if (type === 'next') return originalElement;
                      if (type === 'jump-prev' || type === 'jump-next') return originalElement;
                      return originalElement;
                    }}
                    showQuickJumper={false}
                    showLessItems={false}
                  />
                  <button
                    type="button"
                    className="custom-pagination-btn"
                    disabled={current === pageCount}
                    onClick={() => setCurrent(pageCount)}
                  >
                    尾页
                  </button>
                </div>
              </div>
            )}
          />
        ) : (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '300px',
              color: '#999',
              fontSize: '16px',
            }}
          >
            {activeTab === 'structured' ? '请选择具体的数据表查看详情' : '请选择数据目录'}
          </div>
        )}
      </div>
      <Modal
        open={deleteModalVisible}
        onCancel={() => setDeleteModalVisible(false)}
        onOk={handleDeleteConfirm}
        okText="确定"
        cancelText="取消"
        title={deleteType === 'single' ? '删除数据' : '批量删除'}
      >
        {deleteType === 'single' ? '确定要删除该数据吗？' : '确定要批量删除选中的数据吗？'}
      </Modal>
    </>
  );
};

export default DataTable;
