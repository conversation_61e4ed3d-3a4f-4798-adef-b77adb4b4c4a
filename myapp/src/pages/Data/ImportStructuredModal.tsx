import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Modal, message, Button, Table, Spin, Input, Select } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { InboxOutlined, DeleteOutlined } from '@ant-design/icons';
// import { uploadRequest } from '@/services/api/request';
import {
  fetchStructuredDataTableFieldList, //获取表字段信息
  checkExistTableStructure, //对齐检查
  getTableStructure, //获取表结构
  updateTableStructure, //更新表结构
  importStructuredData, //导入结构化数据
  fetchStructuredDataFieldTypeConfig, //获取字段类型配置
} from '@/services/api/dataCategory';
import type { ColumnsType } from 'antd/es/table';

// 表字段信息接口
interface TableFieldItem {
  fieldId: string;
  fieldName: string;
  columnName: string;
  columnType: string;
  fieldLength: number;
  comment: string;
  isPrimaryKey: boolean;
}

// 表结构配置项接口
interface TableStructureItem {
  key: string;
  fieldName: string;
  columnName: string;
  description: string;
  dataType: string;
  maxLength: string;
  operation: string;
}

// 中间省略号函数
function middleEllipsis(str: string, maxLength: number) {
  if (str.length <= maxLength) {
    return str;
  }

  const start = Math.ceil((maxLength - 3) / 2);
  const end = Math.floor((maxLength - 3) / 2);

  return str.slice(0, start) + '...' + str.slice(-end);
}

// 获取文件类型图标
function getFileTypeIcon(fileName: string) {
  const extension = fileName.split('.').pop()?.toLowerCase();
  switch (extension) {
    case 'xlsx':
    case 'xls':
      return '/icons/excel.svg';
    case 'csv':
      return '/icons/excel.svg'; // 或有csv专用icon可替换
    default:
      return '/icons/file.svg';
  }
}

interface ImportStructuredModalProps {
  open: boolean;
  onCancel: () => void;
  categoryName?: string;
  categoryId?: string;
  onSuccess?: () => void;
}

export interface StructuredUploaderProps {
  categoryId?: string;
  maxSize?: number;
  onSuccess?: (result: any) => void;
  onError?: (err: any) => void;
  onFileSelected?: (file: UploadFile) => Promise<boolean>;
  style?: React.CSSProperties;
  file?: UploadFile | null;
  uploadProgress?: number;
  onProgress?: (progress: number) => void;
}

export interface StructuredUploaderRef {
  clear: () => void;
  getFile: () => UploadFile | null;
  uploading: boolean;
}

export const StructuredUploader = forwardRef<StructuredUploaderRef, StructuredUploaderProps>(
  (props, ref) => {
    const {
      maxSize = 20 * 1024 * 1024,
      onSuccess,
      style,
      file: controlledFile,
      uploadProgress: controlledProgress,
      onProgress,
    } = props;
    const [file, setFile] = useState<UploadFile | null>(controlledFile ?? null);
    const [uploading] = useState(false);
    const [uploadProgress, setUploadProgress] = useState(0);
    const fileRef = useRef<UploadFile | null>(null);

    useImperativeHandle(
      ref,
      () => ({
        clear: () => {
          setFile(null);
          setUploadProgress(0);
          fileRef.current = null;
        },
        getFile: () => fileRef.current,
        uploading,
      }),
      [uploading],
    );

    // 受控 file 同步
    useEffect(() => {
      if (controlledFile !== undefined) {
        setFile(controlledFile);
        fileRef.current = controlledFile;
      }
    }, [controlledFile]);

    // 受控进度同步
    useEffect(() => {
      if (controlledProgress !== undefined) {
        setUploadProgress(controlledProgress);
      }
    }, [controlledProgress]);

    // 选择文件
    const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFile = e.target.files?.[0];
      if (!selectedFile) return;

      if (selectedFile.size > maxSize) {
        message.error(`文件大小超过${maxSize / 1024 / 1024}MB限制`);
        e.target.value = '';
        return;
      }

      const uploadFile: UploadFile = {
        uid: `${Date.now()}-${selectedFile.name}`,
        name: selectedFile.name,
        status: 'done',
        originFileObj: selectedFile as any,
      };

      setFile(uploadFile);
      fileRef.current = uploadFile;
      setUploadProgress(0);
      e.target.value = '';

      // 在文件选择后检查已有数据表结构
      if (props.onFileSelected) {
        const shouldProceed = await props.onFileSelected(uploadFile);
        if (!shouldProceed) {
          // 如果检查失败，清除文件选择
          setFile(null);
          fileRef.current = null;
          return;
        }
      }

      // 选择文件后直接显示进度条为100
      setUploadProgress(100);
      if (onProgress) onProgress(100);
      // 取消自动上传，改为调用onSuccess回调
      if (onSuccess) {
        onSuccess({ success: true, message: '文件选择成功' });
      }
    };

    // 移除文件
    const handleRemove = () => {
      setFile(null);
      setUploadProgress(0);
      fileRef.current = null;
    };

    return (
      <div style={style}>
        <input
          type="file"
          accept=".xlsx,.xls,.csv"
          onChange={handleFileChange}
          style={{ display: 'none' }}
          id="structured-upload-input"
        />
        <label
          htmlFor="structured-upload-input"
          className="upload-area"
          style={{
            display: 'block',
            borderRadius: 4,
            padding: 32,
            textAlign: 'center',
            cursor: 'pointer',
            background: '#fafafa',
          }}
        >
          <InboxOutlined
            className="upload-area-icon"
            style={{ color: '#bfbfbf', fontSize: 48, transition: 'transform 0.2s' }}
          />
          <div style={{ color: '#999', fontSize: 16, marginTop: 8 }}>点击选择文件</div>
          <div style={{ color: '#bfbfbf', fontSize: 12, marginTop: 4 }}>
            1.上传一份excel或csv格式的文件，文件大小限制{maxSize / 1024 / 1024}MB
          </div>
          <div style={{ color: '#bfbfbf', fontSize: 12, marginTop: 4 }}>
            2.若增量更新，新上传文件的表结构需要与现有对齐，否则上传失败
          </div>
        </label>
        {/* 文件显示 */}
        {file && (
          <div className="upload-file-list" style={{ marginTop: 16 }}>
            <div
              style={{
                position: 'relative',
                marginBottom: 6,
                borderRadius: 6,
                overflow: 'hidden',
                background: '#fff',
                boxShadow: '0 1px 4px rgba(0,0,0,0.03)',
              }}
            >
              {/* 进度背景 */}
              <div
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  height: '100%',
                  width: `${
                    controlledProgress !== undefined ? controlledProgress : uploadProgress
                  }%`,
                  background:
                    (controlledProgress !== undefined ? controlledProgress : uploadProgress) === 100
                      ? '#0aac79'
                      : 'linear-gradient(90deg, #e6fff3 0%, #b6f3d7 100%)',
                  opacity: 0.18,
                  zIndex: 1,
                  transition: 'width 0.3s',
                }}
              />
              <div
                style={{
                  position: 'relative',
                  zIndex: 2,
                  display: 'flex',
                  alignItems: 'center',
                  padding: '8px 12px',
                }}
              >
                <img
                  src={getFileTypeIcon(file.name)}
                  alt="file type"
                  style={{ width: 16, height: 16, marginRight: 8 }}
                />
                <span
                  style={{
                    flex: 1,
                    fontSize: 14,
                    color: '#333',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {middleEllipsis(file.name, 30)}
                </span>
                <Button
                  type="text"
                  size="small"
                  className="upload-file-remove-btn"
                  onClick={handleRemove}
                  disabled={uploading}
                  icon={<DeleteOutlined style={{ color: '#222', fontSize: 16 }} />}
                  style={{
                    marginLeft: 4,
                    padding: 0,
                    boxShadow: 'none',
                    background: 'none',
                    width: 12,
                    height: 12,
                  }}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    );
  },
);

const ImportStructuredModal: React.FC<ImportStructuredModalProps> = ({
  open,
  onCancel,
  categoryName,
  categoryId,
  onSuccess,
}) => {
  const uploaderRef = useRef<StructuredUploaderRef>(null);
  const [confirming] = useState(false);
  const [tableFields, setTableFields] = useState<TableFieldItem[]>([]);
  const [loadingFields, setLoadingFields] = useState(false);
  const [showNextStep, setShowNextStep] = useState(false);
  const [tableStructure, setTableStructure] = useState<TableStructureItem[]>([
    {
      key: '1',
      fieldName: '列名',
      columnName: '字段名称',
      description: '请输入',
      dataType: 'string',
      maxLength: '0/100',
      operation: '删除',
    },
    {
      key: '2',
      fieldName: '款/项/目内容',
      columnName: '字段名称',
      description: '请输入',
      dataType: 'string',
      maxLength: '0/100',
      operation: '删除',
    },
    {
      key: '3',
      fieldName: '负面示例',
      columnName: '字段名称',
      description: '请输入',
      dataType: 'string',
      maxLength: '0/100',
      operation: '删除',
    },
    {
      key: '4',
      fieldName: '命中词',
      columnName: '字段名称',
      description: '请输入',
      dataType: 'string',
      maxLength: '0/100',
      operation: '删除',
    },
    {
      key: '5',
      fieldName: '提示语',
      columnName: '字段名称',
      description: '请输入',
      dataType: 'string',
      maxLength: '0/100',
      operation: '删除',
    },
  ]);
  // 新增：保存已上传文件信息
  const [uploadedFile, setUploadedFile] = useState<UploadFile | null>(null);
  // 新增：保存进度条状态
  const [uploadProgress, setUploadProgress] = useState(0);
  // 新增：表结构解析 loading 状态
  const [loadingTableStructure, setLoadingTableStructure] = useState(false);
  // 新增：字段类型配置
  const [fieldTypeConfig, setFieldTypeConfig] = useState<Record<string, boolean>>({});



  // 获取表字段信息
  useEffect(() => {
    if (open && categoryId && categoryId !== 'structured_root') {
      setLoadingFields(true);
      fetchStructuredDataTableFieldList({ tableId: categoryId })
        .then((result) => {
          if (result.success && result.result) {
            setTableFields(result.result);
            console.log('表字段信息:', result.result);
          } else {
            console.warn('获取表字段信息失败:', result.message);
            setTableFields([]);
          }
        })
        .catch((error) => {
          console.error('获取表字段信息出错:', error);
          setTableFields([]);
        })
        .finally(() => {
          setLoadingFields(false);
        });
    } else {
      setTableFields([]);
      setLoadingFields(false);
    }
  }, [open, categoryId]);

  // 获取字段类型配置
  useEffect(() => {
    if (open) {
      fetchStructuredDataFieldTypeConfig()
        .then((result) => {
          if (result.success && result.result) {
            setFieldTypeConfig(result.result);
            console.log('字段类型配置:', result.result);
          } else {
            console.warn('获取字段类型配置失败:', result.message);
            setFieldTypeConfig({});
          }
        })
        .catch((error) => {
          console.error('获取字段类型配置出错:', error);
          setFieldTypeConfig({});
        });
    }
  }, [open]);

  /**
   * 处理文件选择时的回调
   * 当已有数据表结构时，调用检查接口
   */
  const handleFileSelected = async (file: UploadFile): Promise<boolean> => {
    console.log('文件已选择:', file);

    if (!categoryId) {
      return true;
    }

    // 如果已有数据表结构，调用检查接口
    if (tableFields.length > 0 && file.originFileObj) {
      try {
        const checkResult = await checkExistTableStructure({
          tableId: categoryId,
          file: file.originFileObj as File,
        });
        console.log('表结构检查结果:', checkResult);

        if (checkResult.success) {
          message.success('表结构检查通过，可以继续上传');
          return true;
        } else {
          message.error(checkResult.message || '表结构检查失败，无法上传');
          return false;
        }
      } catch (error) {
        console.error('表结构检查出错:', error);
        message.error('表结构检查请求失败');
        return false;
      }
    }

    // 如果没有已有数据表结构，直接允许上传
    return true;
  };

  /**
   * 处理文件上传成功后的回调
   * 当已有数据表结构为空时，调用获取表结构接口
   */
  const handleUploadSuccess = async (result: any) => {
    console.log('文件上传成功:', result);

    if (tableFields.length === 0 && categoryId) {
      setLoadingTableStructure(true);
      try {
        const structureResult = await getTableStructure({
          tableId: categoryId,
          file: uploaderRef.current?.getFile()?.originFileObj as File,
        });
        console.log('获取表结构结果:', structureResult);

        if (structureResult.success && structureResult.result) {
          // 将getTableStructure接口返回的数据转换为表结构配置格式
          const structureData: TableStructureItem[] = structureResult.result.map(
            (item: any, index: number) => ({
              key: item.fieldId || `${index + 1}`,
              fieldName: item.fieldName || item.columnName || '',
              columnName: item.columnName || '',
              description: item.comment || '请输入',
              dataType: item.columnType || 'string',
              maxLength: item.fieldLength?.toString() || '',
              operation: '删除',
            }),
          );
          setTableStructure(structureData);
          message.success('获取表结构成功');
        } else {
          message.warning(structureResult.message || '获取表结构失败');
        }
      } catch (error) {
        console.error('获取表结构出错:', error);
        message.warning('获取表结构请求失败');
      } finally {
        setLoadingTableStructure(false);
      }
    }
    setUploadedFile(uploaderRef.current?.getFile() || null);
    // 不再自动关闭弹窗
    // if (onOk) onOk();
  };

  /**
   * 将已有表字段数据转换为表结构配置格式
   * 用于在有已有数据表结构时显示配置
   */
  const convertTableFieldsToStructure = (fields: TableFieldItem[]): TableStructureItem[] => {
    return fields.map((field, index) => ({
      key: field.fieldId || `${index + 1}`,
      fieldName: field.fieldName || field.columnName || '',
      columnName: field.columnName || '',
      description: field.comment || '请输入',
      dataType: field.columnType || 'string',
      maxLength: field.fieldLength?.toString() || '',
      operation: '删除',
    }));
  };

  /**
   * 当有已有数据表结构时，将其转换为表结构配置
   */
  useEffect(() => {
    if (tableFields.length > 0) {
      const structureData = convertTableFieldsToStructure(tableFields);
      setTableStructure(structureData);
    }
  }, [tableFields]);

  // 表字段信息表格列配置
  const fieldColumns: ColumnsType<TableFieldItem> = [
    {
      title: '列名',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 100,
    },
    {
      title: '字段名称',
      dataIndex: 'columnName',
      key: 'columnName',
      width: 130,
    },
    {
      title: '数据类型',
      dataIndex: 'columnType',
      key: 'columnType',
      width: 100,
    },
    {
      title: '字段长度',
      dataIndex: 'fieldLength',
      key: 'fieldLength',
      width: 80,
    },
    {
      title: '备注',
      dataIndex: 'comment',
      key: 'comment',
      ellipsis: true,
    },
    // {
    //   title: '主键',
    //   dataIndex: 'isPrimaryKey',
    //   key: 'isPrimaryKey',
    //   width: 60,
    //   render: (isPrimaryKey: boolean) => (isPrimaryKey ? '是' : '否'),
    // },
  ];

  /**
   * 实时同步表结构到后端
   */
  const postTableStructure = (newStructure: TableStructureItem[]) => {
    if (!categoryId) return;
    if (newStructure.length === 0) {
      message.warning('表结构不能为空');
      return;
    }
    updateTableStructure({
      tableId: categoryId,
      configs: newStructure.map((item) => ({
        fieldId: item.key,
        fieldName: item.fieldName,
        columnName: item.columnName,
        columnType: item.dataType, // 直接使用原始类型
        fieldLength: parseInt(item.maxLength || '255', 10),
        comment: item.description,
        isPrimaryKey: false, // 如有主键字段请补充
      })),
    });
  };

  /**
   * 处理取消按钮点击事件
   */
  const handleCancel = () => {
    if (uploaderRef.current) {
      uploaderRef.current.clear();
    }
    setShowNextStep(false);
    onCancel();
    setUploadedFile(null);
    setUploadProgress(0);
  };

  // 是否允许进入下一步（有无表结构都必须上传文件）
  const canGoNext = !!uploadedFile && !loadingTableStructure;
  // 第二步表结构是否可编辑（无表结构时可编辑，有表结构时不可编辑）
  const structureEditable = tableFields.length === 0;

  /**
   * 处理确定按钮点击事件
   * 切换到下一步状态
   */
  const handleOk = async () => {
    if (!showNextStep) {
      // 新增校验：只有 canGoNext 时才能进入下一步
      if (!canGoNext) {
        message.warning('请先上传数据文件');
        return;
      }
      setShowNextStep(true);
    } else {
      // 在下一步状态下点击导入按钮的逻辑
      if (!categoryId) {
        message.error('缺少 tableId');
        return;
      }
      try {
        const res = await importStructuredData({ tableId: categoryId });
        if (res && res.success) {
          message.success('导入成功');
          handleCancel();
          // 调用成功回调，通知父组件刷新数据
          if (onSuccess) {
            onSuccess();
          }
        } else {
          message.error(res?.message || '导入失败');
        }
      } catch (e) {
        message.error('导入请求失败');
      }
    }
  };

  /**
   * 处理表结构数据类型变更
   */
  const handleDataTypeChange = (key: string, value: string) => {
    setTableStructure((prev) => {
      const newStructure = prev.map((item) =>
        item.key === key ? { ...item, dataType: value } : item,
      );
      postTableStructure(newStructure);
      return newStructure;
    });
  };

  /**
   * 处理表结构描述变更
   */
  const handleDescriptionChange = (key: string, value: string) => {
    setTableStructure((prev) => {
      const newStructure = prev.map((item) =>
        item.key === key ? { ...item, description: value } : item,
      );
      postTableStructure(newStructure);
      return newStructure;
    });
  };

  /**
   * 处理表结构 fieldName 变更
   */
  const handleFieldNameChange = (key: string, value: string) => {
    setTableStructure((prev) => {
      const newStructure = prev.map((item) =>
        item.key === key ? { ...item, fieldName: value } : item,
      );
      postTableStructure(newStructure);
      return newStructure;
    });
  };

  /**
   * 处理表结构 columnName 变更
   */
  const handleColumnNameChange = (key: string, value: string) => {
    setTableStructure((prev) => {
      const newStructure = prev.map((item) =>
        item.key === key ? { ...item, columnName: value } : item,
      );
      postTableStructure(newStructure);
      return newStructure;
    });
  };

  /**
   * 处理表结构数据长度变更
   */
  const handleMaxLengthChange = (key: string, value: string) => {
    setTableStructure((prev) => {
      const newStructure = prev.map((item) =>
        item.key === key ? { ...item, maxLength: value } : item,
      );
      postTableStructure(newStructure);
      return newStructure;
    });
  };

  /**
   * 处理删除表结构行
   */
  const handleDeleteRow = (key: string) => {
    setTableStructure((prev) => {
      const newStructure = prev.filter((item) => item.key !== key);
      postTableStructure(newStructure);
      return newStructure;
    });
  };

  // 表结构配置表格列定义
  const structureColumns: ColumnsType<TableStructureItem> = [
    {
      title: '列名',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 120,
      render: (text: string, record: TableStructureItem) => (
        <Input
          value={text}
          placeholder="请输入"
          onChange={(e) => handleFieldNameChange(record.key, e.target.value)}
          style={{ border: 'none', padding: 0, fontSize: 14 }}
          disabled={!structureEditable}
        />
      ),
    },
    {
      title: '字段名称',
      dataIndex: 'columnName',
      key: 'columnName',
      width: 120,
      render: (text: string, record: TableStructureItem) => (
        <Input
          value={text}
          placeholder="请输入"
          onChange={(e) => handleColumnNameChange(record.key, e.target.value)}
          style={{ border: 'none', padding: 0, fontSize: 14 }}
          disabled={!structureEditable}
        />
      ),
    },
    {
      title: '数据类型',
      dataIndex: 'dataType',
      key: 'dataType',
      width: 120,
      render: (text: string, record: TableStructureItem) => (
        <Select
          value={text}
          onChange={(value) => handleDataTypeChange(record.key, value)}
          style={{ width: '100%', border: 'none' }}
          variant="borderless"
          disabled={!structureEditable}
        >
          {Object.entries(fieldTypeConfig).map(([type]) => (
            <Select.Option key={type} value={type}>
              {type}
            </Select.Option>
          ))}
        </Select>
      ),
    },
    {
      title: '数据长度',
      dataIndex: 'maxLength',
      key: 'maxLength',
      width: 100,
      render: (text: string, record: TableStructureItem) => {
        const isDataTypeEnabled = fieldTypeConfig[record.dataType] || false;
        return (
          <Input
            value={text}
            placeholder="请输入"
            onChange={(e) => handleMaxLengthChange(record.key, e.target.value)}
            style={{ border: 'none', padding: 0, fontSize: 14 }}
            disabled={!structureEditable || !isDataTypeEnabled}
          />
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      render: (text: string, record: TableStructureItem) => (
        <Input
          value={text}
          placeholder="请输入"
          onChange={(e) => handleDescriptionChange(record.key, e.target.value)}
          style={{ border: 'none', padding: 0, fontSize: 14 }}
          disabled={!structureEditable}
        />
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 80,
      render: (text: string, record: TableStructureItem) => (
        <Button
          type="link"
          danger
          size="small"
          onClick={() => handleDeleteRow(record.key)}
          style={{ padding: 0, fontSize: 14 }}
          disabled={!structureEditable}
        >
          删除
        </Button>
      ),
    },
  ];

  // 封装：刷新表字段信息
  const refreshTableFields = async (categoryId: string) => {
    setLoadingFields(true);
    try {
      const result = await fetchStructuredDataTableFieldList({ tableId: categoryId });
      if (result.success && result.result) {
        setTableFields(result.result);
      } else {
        setTableFields([]);
      }
    } catch {
      setTableFields([]);
    } finally {
      setLoadingFields(false);
    }
  };

  // 封装：刷新表结构配置
  const refreshTableStructure = async (categoryId: string, file: File) => {
    setLoadingTableStructure(true);
    try {
      const structureResult = await getTableStructure({
        tableId: categoryId,
        file,
      });
      if (structureResult.success && structureResult.result) {
        const structureData = structureResult.result.map((item: any) => ({
          key: item.fieldId,
          fieldName: item.fieldName || item.columnName || '',
          columnName: item.columnName || '',
          description: item.comment || '请输入',
          dataType: item.columnType || 'string',
          maxLength: item.fieldLength?.toString() || '',
          operation: '删除',
        }));
        setTableStructure(structureData);
      } else {
        setTableStructure([]);
      }
    } finally {
      setLoadingTableStructure(false);
    }
  };

  return (
    <Modal
      open={open}
      title={showNextStep ? '导入结构化数据' : '导入结构化数据'}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        ...(showNextStep
          ? [
              <Button
                key="back"
                onClick={async () => {
                  if (categoryId) {
                    await refreshTableFields(categoryId);
                  }
                  setTableStructure([]);
                  setShowNextStep(false);
                }}
              >
                上一步
              </Button>,
              <Button key="import" type="primary" onClick={handleOk}>
                导入
              </Button>,
            ]
          : [
              <Button
                key="next"
                type="primary"
                onClick={async () => {
                  if (!canGoNext) return;
                  if (categoryId && uploadedFile) {
                    if (tableFields.length > 0) {
                      // 有表结构时，刷新表结构后再进入下一步
                      await refreshTableStructure(categoryId, uploadedFile.originFileObj as File);
                      setShowNextStep(true);
                    } else {
                      // 无表结构时，直接进入下一步
                      setShowNextStep(true);
                    }
                  } else {
                    setShowNextStep(true);
                  }
                }}
                disabled={!canGoNext}
              >
                下一步
              </Button>,
            ]),
      ]}
      confirmLoading={confirming}
      width={showNextStep ? 800 : 600}
      destroyOnHidden
    >
      {!showNextStep ? (
        // 第一步：文件上传
        <>
          <div style={{ marginBottom: 16, fontWeight: 500 }}>文件位置</div>
          <input
            style={{
              width: '100%',
              marginBottom: 16,
              height: 32,
              borderRadius: 4,
              border: '1px solid #e5e6eb',
              padding: '0 8px',
            }}
            placeholder="文件位置"
            value={categoryName || categoryId || ''}
            disabled
          />
          <StructuredUploader
            ref={uploaderRef}
            categoryId={categoryId}
            onSuccess={handleUploadSuccess}
            onFileSelected={handleFileSelected}
            file={uploadedFile}
            uploadProgress={uploadProgress}
            onProgress={setUploadProgress}
          />

          {/* 表字段信息展示 */}
          {categoryId && categoryId !== 'structured_root' && tableFields.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <div style={{ marginBottom: 8, fontWeight: 500 }}>已有数据表结构</div>
              <Spin spinning={loadingFields}>
                <Table
                  columns={fieldColumns}
                  dataSource={tableFields}
                  rowKey="fieldId"
                  size="small"
                  pagination={false}
                  scroll={{ y: 200 }}
                />
              </Spin>
            </div>
          )}
          {/* 无表字段时提示
          {categoryId && categoryId !== 'structured_root' && tableFields.length === 0 && (
            <div style={{ color: '#faad14', marginTop: 16, textAlign: 'center' }}>请先上传数据文件</div>
          )} */}
        </>
      ) : (
        // 第二步：表结构配置
        <>
          {/* 文件名和图标展示 */}
          {uploadedFile && (
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
              <img
                src={getFileTypeIcon(uploadedFile.name)}
                alt="file type"
                style={{ width: 18, height: 18, marginRight: 8 }}
              />
              <span>{uploadedFile.name}</span>
            </div>
          )}
          <div style={{ marginBottom: 16, fontWeight: 500 }}>表结构配置</div>

          <Table
            columns={structureColumns}
            dataSource={tableStructure}
            rowKey="key"
            size="small"
            pagination={false}
            scroll={{ y: 300 }}
            style={{ marginBottom: 16 }}
          />
        </>
      )}
    </Modal>
  );
};

export default ImportStructuredModal;
