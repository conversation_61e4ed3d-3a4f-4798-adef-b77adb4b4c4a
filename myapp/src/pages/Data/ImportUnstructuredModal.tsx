import React, { useState, useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Modal, message, Button } from 'antd';
import type { UploadFile } from 'antd/es/upload/interface';
import { InboxOutlined, DeleteOutlined } from '@ant-design/icons';
import { uploadRequest, createSSEConnection } from '@/services/api/request';
import { confirmImportUnstructuredData, deleteImportFile,cancelImportUnstructuredData } from '@/services/api/dataCategory';

// 用于跨组件/回调记录最后一次上传的sessionId
const lastSessionIdRef = { current: null as string | null };
// sessionId只在未确定前保持不变，全局唯一
const sessionIdRef = { current: null as string | null };

// 文件名中间省略辅助函数
function middleEllipsis(str: string, maxLength: number) {
  if (str.length <= maxLength) return str;
  const dotIndex = str.lastIndexOf('.');
  if (dotIndex === -1 || dotIndex === 0) {
    // 无后缀或隐藏文件，直接中间省略
    const keep = Math.floor((maxLength - 3) / 2);
    return str.slice(0, keep) + '...' + str.slice(-keep);
  }
  const name = str.slice(0, dotIndex);
  const ext = str.slice(dotIndex); // 包含点
  const maxNameLen = maxLength - ext.length;
  if (maxNameLen <= 3) {
    // 文件名太短，无法省略，直接保留前1+...+后1
    return name.slice(0, 1) + '...' + name.slice(-1) + ext;
  }
  if (name.length <= maxNameLen) return name + ext;
  const keep = Math.floor((maxNameLen - 3) / 2);
  return name.slice(0, keep) + '...' + name.slice(-keep) + ext;
}

// 在顶部添加文件类型icon辅助函数
function getFileTypeIcon(fileName: string) {
  const extension = fileName.toLowerCase().split('.').pop();
  switch (extension) {
    case 'pdf':
      return '/icons/pdf.svg';
    case 'xls':
    case 'xlsx':
      return '/icons/excel.svg';
    case 'doc':
    case 'docx':
      return '/icons/word.svg';
    case 'ppt':
    case 'pptx':
      return '/icons/ppt.svg';
    case 'png':
    case 'jpg':
    case 'jpeg':
      return '/icons/image.svg';
    default:
      return '/icons/file.svg';
  }
}

interface ImportUnstructuredModalProps {
  open: boolean;
  onCancel: () => void;
  onOk: (files: UploadFile[]) => void;
  categoryName?: string;
  categoryId?: string;
  onRefreshTable?: () => void;
}

export interface UnstructuredBatchUploaderProps {
  categoryId?: string;
  maxCount?: number;
  maxSize?: number;
  onSuccess?: (result: any) => void;
  onError?: (err: any) => void;
  onProgress?: (progress: any) => void;
  style?: React.CSSProperties;
}

export interface UnstructuredBatchUploaderRef {
  clear: () => void;
  getFileList: () => UploadFile[];
}

export const UnstructuredBatchUploader = forwardRef<UnstructuredBatchUploaderRef, UnstructuredBatchUploaderProps>((props, ref) => {
  const { categoryId, maxCount = 10, maxSize = 20 * 1024 * 1024, onSuccess, onError, onProgress, style } = props;
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [fileId: string]: any }>({});
  const sseConnectionRef = useRef<EventSource | null>(null);
  const fileListRef = useRef<UploadFile[]>([]);
  const [hasUploaded, setHasUploaded] = useState(false);
  const prevFileListRef = useRef<UploadFile[]>([]);
  // 记录已上传文件的uid集合
  const uploadedUidSet = useRef<Set<string>>(new Set());

  useImperativeHandle(ref, () => ({
    clear: () => {
      setFileList([]);
      setUploadProgress({});
      if (sseConnectionRef.current) {
        sseConnectionRef.current.close();
        sseConnectionRef.current = null;
      }
    },
    getFileList: () => fileListRef.current,
  }), []);

  useEffect(() => {
    return () => {
      if (sseConnectionRef.current) {
        sseConnectionRef.current.close();
        sseConnectionRef.current = null;
      }
    };
  }, []);

  const generateSessionId = (): string => {
    let sessionId = '';
    for (let i = 0; i < 25; i++) {
      sessionId += Math.floor(Math.random() * 10).toString();
    }
    return sessionId;
  };

  const establishSSEConnection = (sessionId: string) => {
    if (sseConnectionRef.current) {
      sseConnectionRef.current.close();
    }
    const eventSource = createSSEConnection(
      `/rule/unstructured-data/query-batch-upload-progress?sessionId=${sessionId}`,
      (data) => {
        let parsedData: any = null;
        if (data && typeof data === 'object') {
          parsedData = data;
        } else if (typeof data === 'string') {
          parsedData = JSON.parse(data);
        }
        const progresses = Array.isArray(parsedData?.progress)
          ? parsedData.progress
          : Array.isArray(parsedData)
            ? parsedData
            : parsedData ? [parsedData] : [];
        if (progresses.length > 0) {
          setUploadProgress((prev) => {
            const next = { ...prev };
            progresses.forEach((item: any) => {
              const fileUid = fileListRef.current.find((f) => f.name === item.fileName)?.uid;
              if (fileUid) {
                next[fileUid] = {
                  ...next[fileUid],
                  ...item,
                };
              }
            });
            return next;
          });
          if (onProgress) onProgress(progresses);
        }
        // 只用totalFiles字段判断关闭SSE
        if (parsedData.totalFiles) {
          eventSource.close();
          sseConnectionRef.current = null;
        }
      },
      () => {
        message.error('进度查询连接失败');
      },
      () => {},
    );
    sseConnectionRef.current = eventSource;
  };

  // 选择文件
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    let newList: UploadFile[] = [...fileList];
    const remain = maxCount - newList.length;
    if (files.length > remain) {
      message.warning(`最多只能上传${maxCount}个文件，本次只添加前${remain}个`);
    }
    // 只取允许的数量
    const filesToAdd = files.slice(0, remain);
    for (const file of filesToAdd) {
      if (file.size > maxSize) {
        message.error(`${file.name} 超过${maxSize / 1024 / 1024}MB，已跳过`);
        continue;
      }
      newList.push({
        uid: `${Date.now()}-${file.name}`,
        name: file.name,
        status: 'done',
        originFileObj: file,
      } as UploadFile);
    }
    setFileList(newList);
    fileListRef.current = newList;
    setHasUploaded(false);
    e.target.value = '';
  };

  // 移除文件
  const handleRemove = async (uid: string) => {
    // 未上传的文件只本地移除
    if (!uploadedUidSet.current.has(uid)) {
      const newList = fileList.filter((f) => f.uid !== uid);
      setFileList(newList);
      fileListRef.current = newList;
      return;
    }
    // 已上传的文件才调接口
    const file = fileList.find((f) => f.uid === uid);
    const sessionId = lastSessionIdRef.current;
    const fileId = uploadProgress[uid]?.fileId;
    if (!file || !categoryId || !sessionId || !fileId) {
      message.error('进度未同步，无法删除，请稍后重试');
      return;
    }
    try {
      await deleteImportFile({
        categoryId,
        sessionId,
        fileId,
      });
      message.success('删除成功');
      const newList = fileList.filter((f) => f.uid !== uid);
      setFileList(newList);
      fileListRef.current = newList;
      uploadedUidSet.current.delete(uid);
      setUploadProgress((prev) => {
        const next = { ...prev };
        delete next[uid];
        return next;
      });
    } catch (e) {
      message.error('删除失败');
    }
  };

  // 批量上传
  const handleBatchUpload = async () => {
    // 只上传未上传过的文件
    const filesToUpload = fileList.filter(f => !uploadedUidSet.current.has(f.uid));
    if (filesToUpload.length === 0) {
      message.info('没有新文件需要上传');
      return;
    }
    if (!categoryId) {
      message.error('请先选择分类');
      if (onError) onError('未选择分类');
      return;
    }
    setUploading(true);
    // 只在第一次上传时生成sessionId，若SSE断开则重连
    if (!sessionIdRef.current) {
      sessionIdRef.current = generateSessionId();
      establishSSEConnection(sessionIdRef.current);
      await new Promise<void>((resolve) => { setTimeout(resolve, 500); });
    } else if (!sseConnectionRef.current) {
      // sessionId存在但SSE已断开，需重连
      establishSSEConnection(sessionIdRef.current);
      await new Promise<void>((resolve) => { setTimeout(resolve, 500); });
    }
    const sessionId = sessionIdRef.current;
    // 初始化进度
    const initProgress: any = {};
    filesToUpload.forEach((file) => {
      initProgress[file.uid] = {
        sessionId,
        fileId: file.uid,
        fileName: file.name,
        progress: 0,
        // message: '准备上传...',
      };
    });
    setUploadProgress(prev => ({ ...prev, ...initProgress }));
    const formData = new FormData();
    formData.append('categoryId', categoryId);
    formData.append('sessionId', sessionId);
    filesToUpload.forEach((file) => {
      formData.append('files', file.originFileObj as File);
    });
    try {
      const result = await uploadRequest('/rule/unstructured-data/batch-upload', formData);
      if (result && (result.sessionId || result.success)) {
        message.success('上传成功');
        if (lastSessionIdRef) lastSessionIdRef.current = sessionId;
        if (onSuccess) onSuccess(result);
        setHasUploaded(true);
        prevFileListRef.current = [...fileList];
        // 记录已上传文件
        filesToUpload.forEach(f => uploadedUidSet.current.add(f.uid));
      } else {
        const errorMsg = result?.message || '上传失败';
        message.error(errorMsg);
        if (onError) onError(errorMsg);
      }
    } catch (e: any) {
      const errorMsg = e?.message || '上传请求失败';
      message.error(errorMsg);
      if (onError) onError(errorMsg);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div style={style}>
      <input
        type="file"
        multiple
        onChange={handleFileChange}
        style={{ display: 'none' }}
        id="unstructured-upload-input"
      />
      <label htmlFor="unstructured-upload-input" className="upload-area" style={{ display: 'block', borderRadius: 4, padding: 32, textAlign: 'center', cursor: 'pointer', background: '#fafafa' }}>
        <InboxOutlined className="upload-area-icon" style={{ color: '#bfbfbf', fontSize: 48, transition: 'transform 0.2s' }} />
        <div style={{ color: '#999', fontSize: 16, marginTop: 8 }}>点击或拖拽文件到此处</div>
        <div style={{ color: '#bfbfbf', fontSize: 12, marginTop: 4 }}>
          支持PDF、TXT、DOC、DOCX、MD、PNG、JPG等格式，每次最多可上传{maxCount}个文件，单个文件不超过{maxSize / 1024 / 1024}MB
        </div>
      </label>
      {/* 文件列表 */}
      {fileList.length > 0 && (
        <div className="upload-file-list">
          {fileList.map((file) => {
            const progress = uploadProgress[file.uid!]?.progress ?? 0;
            const isDone = progress === 100;
            return (
              <div
                key={file.uid}
                style={{
                  position: 'relative',
                  marginBottom: 6,
                  borderRadius: 6,
                  overflow: 'hidden',
                  background: '#fff',
                  boxShadow: '0 1px 4px rgba(0,0,0,0.03)',
                }}
              >
                {/* 进度背景 */}
                <div
                  style={{
                    position: 'absolute',
                    left: 0,
                    top: 0,
                    height: '100%',
                    width: `${progress}%`,
                    background: isDone ? '#0aac79' : 'linear-gradient(90deg, #e6fff3 0%, #b6f3d7 100%)',
                    opacity: 0.18,
                    zIndex: 1,
                    transition: 'width 0.3s',
                  }}
                />
                <div style={{ position: 'relative', zIndex: 2, display: 'flex', alignItems: 'center', padding: '8px 12px' }}>
                  <img
                    src={getFileTypeIcon(file.name)}
                    alt="file type"
                    style={{ width: 18, height: 18, marginRight: 8, verticalAlign: 'middle' }}
                  />
                  <span
                    style={{
                      flex: 1,
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      display: 'inline-block',
                      verticalAlign: 'middle',
                      direction: 'ltr',
                    }}
                    title={file.name}
                  >
                    {middleEllipsis(file.name, 30)}
                  </span>
                  <span style={{ color: isDone ? '#0aac79' : '#666', fontWeight: 600, fontSize: 13, minWidth: 38, marginLeft: 8 }}>
                    {progress}%
                  </span>
                  <Button
                    type="text"
                    size="small"
                    className="upload-file-remove-btn"
                    onClick={() => handleRemove(file.uid!)}
                    disabled={uploading}
                    icon={<DeleteOutlined style={{ color: '#222', fontSize: 16 }} />}
                    style={{ marginLeft: 4, padding: 0, boxShadow: 'none', background: 'none', width: 12, height: 12 }}
                  />
                </div>
                {uploadProgress[file.uid!]?.message && (
                  <div style={{ fontSize: 12, color: isDone ? '#0aac79' : '#666', margin: '0 12px 8px 38px', position: 'relative', zIndex: 2 }}>
                    {uploadProgress[file.uid!].message}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
      <Button
        type="primary"
        style={{ marginTop: 16, width: '100%' }}
        loading={uploading}
        onClick={handleBatchUpload}
        disabled={uploading || fileList.length === 0 || (hasUploaded && JSON.stringify(fileList) === JSON.stringify(prevFileListRef.current))}
      >
        批量上传
      </Button>
    </div>
  );
});

const ImportUnstructuredModal: React.FC<ImportUnstructuredModalProps> = ({
  open,
  onCancel,
  onOk,
  categoryName,
  categoryId,
  onRefreshTable,
}) => {
  const uploaderRef = useRef<UnstructuredBatchUploaderRef>(null);
  const [confirming, setConfirming] = useState(false);
  const [okVisible, setOkVisible] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      const files = uploaderRef.current?.getFileList?.() || [];
      setOkVisible(files.length > 0 && !!lastSessionIdRef.current);
    }, 300);
    return () => clearInterval(interval);
  }, [open]);

  const handleOk = async () => {
    if (uploaderRef.current) {
      setConfirming(true);
      const fileList = uploaderRef.current.getFileList();
      const sessionId = lastSessionIdRef.current;
      if (categoryId && sessionId) {
        try {
          await confirmImportUnstructuredData({ categoryId, sessionId });
          message.success('确认导入成功');
          setTimeout(() => {
            if (typeof onRefreshTable === 'function') onRefreshTable();
            onOk(fileList);
            if (uploaderRef.current) uploaderRef.current.clear();
            sessionIdRef.current = null; // 清空sessionId
            setConfirming(false);
          }, 2000);
          return;
        } catch (e) {
          message.error('确认导入失败');
          setConfirming(false);
          return;
        }
      }
      if (typeof onRefreshTable === 'function') onRefreshTable();
      onOk(fileList);
      uploaderRef.current.clear();
      sessionIdRef.current = null;
      setConfirming(false);
    }
  };

  const handleCancel = async () => {
    if (uploaderRef.current) {
      uploaderRef.current.clear();
    }
    if (sessionIdRef.current && categoryId) {
      try {
        await cancelImportUnstructuredData({ categoryId, sessionId: sessionIdRef.current });
      } catch (e) {
        // 可忽略错误
      } finally {
        sessionIdRef.current = null;
      }
    } else {
      sessionIdRef.current = null;
    }
    onCancel();
  };

  return (
    <Modal
      open={open}
      title="导入非结构化数据"
      onCancel={handleCancel}
      onOk={handleOk}
      okText="确定"
      confirmLoading={confirming}
      cancelText="取消"
      width={600}
      destroyOnHidden
      okButtonProps={{ style: { display: okVisible ? 'inline-block' : 'none' } }}
    >
      <div style={{ marginBottom: 16, fontWeight: 500 }}>文件位置</div>
      <input
        style={{
          width: '100%',
          marginBottom: 16,
          height: 32,
          borderRadius: 4,
          border: '1px solid #e5e6eb',
          padding: '0 8px',
        }}
        placeholder="文件位置"
        value={categoryName || categoryId || ''}
        disabled
      />
      <UnstructuredBatchUploader ref={uploaderRef} categoryId={categoryId} maxCount={10} />
    </Modal>
  );
};

export default ImportUnstructuredModal;
