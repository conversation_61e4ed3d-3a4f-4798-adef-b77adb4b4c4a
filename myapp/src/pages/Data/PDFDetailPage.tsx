import React, { useState, useEffect, useRef } from 'react';
import { message } from 'antd';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/src/Page/AnnotationLayer.css';

// 配置PDF.js worker - 使用本地版本
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.js';

// 文件类型图标
function getFileTypeIcon(fileName: string) {
  const ext = fileName.toLowerCase().split('.').pop();
  switch (ext) {
    case 'pdf':
      return '/icons/pdf.svg';
    case 'xls':
    case 'xlsx':
      return '/icons/excel.svg';
    case 'doc':
    case 'docx':
      return '/icons/word.svg';
    case 'ppt':
    case 'pptx':
      return '/icons/ppt.svg';
    default:
      return '/icons/file.svg';
  }
}

interface FileItem {
  id: string;
  name: string;
  middelFileUrl?: string;
  originFileUrl?: string;
}

interface PDFDetailPageProps {
  files: FileItem[];
  currentFile: FileItem;
  onFileSelect: (file: FileItem) => void;
}

const PDFDetailPage: React.FC<PDFDetailPageProps> = ({ files = [], currentFile, onFileSelect }) => {
  // PDF相关状态
  const [pdfInfo, setPdfInfo] = useState<any[]>([]);
  const [numPages, setNumPages] = useState<number>(0);
  const [pdfBlobUrl, setPdfBlobUrl] = useState<string | undefined>(undefined);
  const [hoverIdx, setHoverIdx] = useState<number | null>(null);
  
  // 新增：缩放控制状态
  const [scale, setScale] = useState<number>(1.0);
  // 当前可见页面（用于显示页码）
  const [currentVisiblePage, setCurrentVisiblePage] = useState<number>(1);

  // PDF文件URL：originFileUrl
  const remotePdfUrl = currentFile.originFileUrl || '/origin.pdf';

  // 页面引用
  const pageRefs = useRef<(HTMLDivElement | null)[]>([]);

  // PDF渲染容器宽度自适应
  const pdfContainerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState<number>(0);

  useEffect(() => {
    if (!pdfContainerRef.current) return;
    const observer = new window.ResizeObserver(entries => {
      for (let entry of entries) {
        setContainerWidth(entry.contentRect.width);
      }
    });
    observer.observe(pdfContainerRef.current);
    return () => observer.disconnect();
  }, []);

  /**
   * 页面跳转函数
   */
  const goToPage = (page: number) => {
    if (page >= 1 && page <= numPages) {
      const pageElement = pageRefs.current[page - 1];
      if (pageElement) {
        pageElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        setCurrentVisiblePage(page);
      }
    }
  };

  const goToPrevPage = () => {
    if (currentVisiblePage > 1) {
      goToPage(currentVisiblePage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentVisiblePage < numPages) {
      goToPage(currentVisiblePage + 1);
    }
  };

  /**
   * 缩放控制函数
   */
  const zoomIn = () => {
    setScale(prev => Math.min(prev + 0.2, 3.0));
  };

  const zoomOut = () => {
    setScale(prev => Math.max(prev - 0.2, 0.5));
  };

  const resetZoom = () => {
    setScale(1.0);
  };



  // 加载结构化json
  useEffect(() => {
    setPdfInfo([]);
    setPdfBlobUrl(undefined);
    // 结构化JSON：middelFileUrl
    if (currentFile.middelFileUrl) {
      fetch(currentFile.middelFileUrl)
        .then((res) => res.json())
        .then((data) => setPdfInfo(data.pdf_info || []))
        .catch(() => setPdfInfo([]));
    } else {
      setPdfInfo([]);
    }
    // 加载pdf
    fetch(remotePdfUrl)
      .then((res) => res.blob())
      .then((blob) => setPdfBlobUrl(URL.createObjectURL(blob)));
    return () => {
      if (pdfBlobUrl) URL.revokeObjectURL(pdfBlobUrl);
    };
    // eslint-disable-next-line
  }, [currentFile]);

  // 获取所有页面的blocks
  const allBlocks = pdfInfo.flatMap((pageData: any, pageIndex: number) =>
    (pageData.preproc_blocks || []).map((block: any, blockIndex: number) => ({
      ...block,
      pageIndex,
      blockIndex,
      globalIndex: pageIndex * 1000 + blockIndex, // 确保全局唯一索引
    })),
  );

  /**
   * 监听滚动位置，更新当前可见页面
   */
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const handleScroll = () => {
      const scrollTop = scrollContainer.scrollTop;
      const containerHeight = scrollContainer.clientHeight;
      const centerY = scrollTop + containerHeight / 2;

      // 找到当前中心位置对应的页面
      for (let i = 0; i < pageRefs.current.length; i++) {
        const pageElement = pageRefs.current[i];
        if (pageElement) {
          const rect = pageElement.getBoundingClientRect();
          const containerRect = scrollContainer.getBoundingClientRect();
          const pageTop = rect.top - containerRect.top + scrollTop;
          const pageBottom = pageTop + rect.height;
          
          if (centerY >= pageTop && centerY <= pageBottom) {
            setCurrentVisiblePage(i + 1);
            break;
          }
        }
      }
    };

    scrollContainer.addEventListener('scroll', handleScroll);
    return () => {
      scrollContainer.removeEventListener('scroll', handleScroll);
    };
  }, [numPages]);

  /**
   * 键盘快捷键支持
   */
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 翻页快捷键
      if (event.key === 'ArrowLeft' || event.key === 'ArrowUp') {
        event.preventDefault();
        goToPrevPage();
      } else if (event.key === 'ArrowRight' || event.key === 'ArrowDown') {
        event.preventDefault();
        goToNextPage();
      }
      
      // 缩放快捷键（Ctrl/Cmd + +/-）
      if ((event.ctrlKey || event.metaKey)) {
        if (event.key === '=' || event.key === '+') {
          event.preventDefault();
          zoomIn();
        } else if (event.key === '-') {
          event.preventDefault();
          zoomOut();
        } else if (event.key === '0') {
          event.preventDefault();
          resetZoom();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentVisiblePage, numPages, scale]);

  return (
    <div style={{ display: 'flex', height: '100vh', background: '#f7fafd' }}>
      {/* 左侧文件列表 */}
      <div
        style={{
          width: '16%',
          background: '#fff',
          borderRight: '1px solid #e5e6eb',
          display: 'flex',
          flexDirection: 'column',
          // height: '100vh',
          // borderRadius: 12,
          // boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
          // margin: 24,
          minWidth: 0,
          padding: 0,
        }}
      >
        {/* 文件列表标题 */}
        <div
          style={{
            // borderBottom: '1px solid #f0f0f0',
            padding: '20px 16px 8px 16px',
            fontWeight: 600,
            fontSize: 16,
            color: '#222',
          }}
        >
          文件列表
        </div>
        <div style={{ flex: 1, overflowY: 'auto', padding: '8px 0' }}>
          {files.map((file) => (
            <div
              key={file.id}
              onClick={() => onFileSelect(file)}
              style={{
                display: 'flex',
                alignItems: 'center',
                height: 40,
                padding: '0 16px',
                cursor: 'pointer',
                background: file.id === currentFile.id ? '#e8faf3' : 'transparent',
                color: file.id === currentFile.id ? '#13C08A' : '#222',
                fontWeight: file.id === currentFile.id ? 600 : 400,
                borderRadius: 6,
                margin: '2px 8px',
                fontSize: 15,
                transition: 'background 0.2s',
              }}
            >
              <img
                src={getFileTypeIcon(file.name)}
                alt="icon"
                style={{ width: 18, height: 18, marginRight: 8 }}
              />
              <span
                style={{
                  flex: 1,
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                }}
              >
                {file.name}
              </span>
            </div>
          ))}
        </div>
      </div>
      {/* 右侧对比区：PDF预览 + 结构化内容 */}
      <div
        style={{
          flex: 1,
          // background: '#fff',
          // padding: 24,
          minWidth: 0,
          display: 'flex',
          flexDirection: 'row',
          // height: 'calc(100vh - 48px)',
          overflow: 'hidden',
          gap: 32,
          padding: '24px',
          borderRadius: '8px',
        }}
      >
        {/* <h2 className='pdf-detail-page-title'>123</h2> */}
        {/* PDF预览区 */}
        <div
          ref={pdfContainerRef}
          style={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minWidth: 0,
            position: 'relative',
            width: '100%',
          }}
        >
          {/* PDF内容滚动区域 */}
          <div
            ref={scrollContainerRef}
            style={{
              flex: 1,
              overflow: 'auto',
              // padding: '24px 0',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
            }}
          >
          {pdfBlobUrl ? (
            <Document
              file={pdfBlobUrl}
              onLoadSuccess={({ numPages }) => {
                setNumPages(numPages);
                setCurrentVisiblePage(1);
              }}
              onLoadError={() => message.error('PDF文件加载失败')}
              onSourceError={() => message.error('PDF源文件错误')}
            >
              {/* 渲染所有页面 */}
              {Array.from({ length: numPages }, (_, index) => {
                const pageNumber = index + 1;
                const pageData = pdfInfo.find((p: any) => p.page_idx === index) || {};
                const pageSize = pageData.page_size || [595, 842];
                // 用自适应宽度
                const baseWidth = containerWidth > 0 ? containerWidth : 600;
                const pdfWidth = baseWidth * scale;
                const pdfHeight = (pdfWidth / pageSize[0]) * pageSize[1];
                const pageBlocks = pageData.preproc_blocks || [];
                const scaleX = pdfWidth / pageSize[0];
                const scaleY = pdfHeight / pageSize[1];

                return (
                  <div
                    key={pageNumber}
                    ref={el => pageRefs.current[index] = el}
                    style={{
                      width: pdfWidth,
                      maxWidth: 'none',
                      height: pdfHeight,
                      background: '#fff',
                      borderRadius: 12,
                      boxShadow: '0 1px 4px rgba(0,0,0,0.03)',
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      margin: '0 auto 24px auto',
                      overflow: 'hidden',
                    }}
                  >
                    <Page
                      pageNumber={pageNumber}
                      width={pdfWidth}
                      onLoadSuccess={() => {}}
                      onLoadError={() => message.error(`第${pageNumber}页加载失败`)}
                    />
                    {/* 高亮块 */}
                    {hoverIdx !== null &&
                      pageBlocks.map((block: any, idx: number) => {
                        const [x1, y1, x2, y2] = block.bbox;
                        const globalIdx = index * 1000 + idx;
                        if (globalIdx !== hoverIdx) return null;
                        return (
                          <div
                            key={idx}
                            style={{
                              position: 'absolute',
                              left: x1 * scaleX,
                              top: y1 * scaleY,
                              width: (x2 - x1) * scaleX,
                              height: (y2 - y1) * scaleY,
                              border: '2px solid #13C08A',
                              background: 'rgba(19,192,138,0.08)',
                              pointerEvents: 'none',
                              transition: 'border 0.2s, background 0.2s',
                              zIndex: 10,
                            }}
                          />
                        );
                      })}
                    {/* 页码标识 */}
                    <div
                      style={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        background: 'rgba(0,0,0,0.7)',
                        color: '#fff',
                        padding: '4px 8px',
                        borderRadius: 4,
                        fontSize: 12,
                        fontWeight: 500,
                        zIndex: 20,
                      }}
                    >
                      {pageNumber} / {numPages}
                    </div>
                  </div>
                );
              })}
            </Document>
          ) : (
            <div
              style={{
                color: '#bbb',
                fontSize: 16,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                height: '200px',
              }}
            >
              PDF加载中...
            </div>
          )}
          </div>
          
          {/* PDF控制面板 */}
          {pdfBlobUrl && numPages > 0 && (
            <div
              style={{
                position: 'absolute',
                background: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: 16,
                zIndex: 100,
                bottom: '2rem', // 距离底部适当缩小
                left: '50%',
                transform: 'translateX(-50%)', // 水平居中
              }}
            >

              
              {/* 缩放控制 */}
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <button
                  type="button"
                  onClick={zoomOut}
                  disabled={scale <= 0.5}
                  style={{
                    width: 32,
                    height: 32,
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    background: scale <= 0.5 ? '#f5f5f5' : '#fff',
                    cursor: scale <= 0.5 ? 'not-allowed' : 'pointer',
                    fontSize: 16,
                    color: scale <= 0.5 ? '#ccc' : '#666',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s',
                  }}
                  onMouseEnter={(e) => {
                    if (scale > 0.5) {
                      e.currentTarget.style.borderColor = '#13C08A';
                      e.currentTarget.style.color = '#13C08A';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (scale > 0.5) {
                      e.currentTarget.style.borderColor = '#d9d9d9';
                      e.currentTarget.style.color = '#666';
                    }
                  }}
                >
                  −
                </button>
                <span
                  style={{
                    minWidth: 60,
                    textAlign: 'center',
                    fontSize: 14,
                    color: '#666',
                    cursor: 'pointer',
                  }}
                  onClick={resetZoom}
                  title="点击重置缩放"
                >
                  {Math.round(scale * 100)}%
                </span>
                <button
                  type="button"
                  onClick={zoomIn}
                  disabled={scale >= 3.0}
                  style={{
                    width: 32,
                    height: 32,
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    background: scale >= 3.0 ? '#f5f5f5' : '#fff',
                    cursor: scale >= 3.0 ? 'not-allowed' : 'pointer',
                    fontSize: 16,
                    color: scale >= 3.0 ? '#ccc' : '#666',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s',
                  }}
                  onMouseEnter={(e) => {
                    if (scale < 3.0) {
                      e.currentTarget.style.borderColor = '#13C08A';
                      e.currentTarget.style.color = '#13C08A';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (scale < 3.0) {
                      e.currentTarget.style.borderColor = '#d9d9d9';
                      e.currentTarget.style.color = '#666';
                    }
                  }}
                >
                  +
                </button>
              </div>
              
              {/* 翻页控制 */}
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <button
                  type="button"
                  onClick={goToPrevPage}
                  disabled={currentVisiblePage <= 1}
                  style={{
                    width: 32,
                    height: 32,
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    background: currentVisiblePage <= 1 ? '#f5f5f5' : '#fff',
                    cursor: currentVisiblePage <= 1 ? 'not-allowed' : 'pointer',
                    fontSize: 16,
                    color: currentVisiblePage <= 1 ? '#ccc' : '#666',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s',
                  }}
                  onMouseEnter={(e) => {
                    if (currentVisiblePage > 1) {
                      e.currentTarget.style.borderColor = '#13C08A';
                      e.currentTarget.style.color = '#13C08A';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentVisiblePage > 1) {
                      e.currentTarget.style.borderColor = '#d9d9d9';
                      e.currentTarget.style.color = '#666';
                    }
                  }}
                >
                  ←
                </button>
                
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <input
                    type="number"
                    min={1}
                    max={numPages}
                    value={currentVisiblePage}
                    onChange={(e) => {
                      const page = parseInt(e.target.value);
                      if (!isNaN(page)) {
                        goToPage(page);
                      }
                    }}
                    style={{
                      width: 60,
                      padding: '6px 8px',
                      border: '1px solid #d9d9d9',
                      borderRadius: 4,
                      textAlign: 'center',
                      fontSize: 14,
                    }}
                  />
                  <span style={{ fontSize: 14, color: '#666' }}>/ {numPages}</span>
                </div>
                
                <button
                  type="button"
                  onClick={goToNextPage}
                  disabled={currentVisiblePage >= numPages}
                  style={{
                    width: 32,
                    height: 32,
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    background: currentVisiblePage >= numPages ? '#f5f5f5' : '#fff',
                    cursor: currentVisiblePage >= numPages ? 'not-allowed' : 'pointer',
                    fontSize: 16,
                    color: currentVisiblePage >= numPages ? '#ccc' : '#666',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'all 0.2s',
                  }}
                  onMouseEnter={(e) => {
                    if (currentVisiblePage < numPages) {
                      e.currentTarget.style.borderColor = '#13C08A';
                      e.currentTarget.style.color = '#13C08A';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (currentVisiblePage < numPages) {
                      e.currentTarget.style.borderColor = '#d9d9d9';
                      e.currentTarget.style.color = '#666';
                    }
                  }}
                >
                  →
                </button>
              </div>
            </div>
          )}
        </div>
        {/* 结构化内容区 */}
        <div
          style={{
            flex: 1,
            overflowY: 'auto',
            minWidth: 0,
            display: 'flex',
            flexDirection: 'column',
            backgroundColor: '#fff',
            borderRadius: '8px',

          }}
        >
          {/* <div
            style={{
              fontWeight: 600,
              fontSize: 16,
              color: '#222',
              marginBottom: 12,
              padding: '24px 16px 0 16px',
              position: 'sticky',
              top: 0,
              background: '#fff',
              zIndex: 10,
            }}
          >
            解析后
          </div> */}
          <div
            style={{
              flex: 1,
              padding: '16px',
              borderRadius: '8px',
            }}
          >
            {allBlocks.map((block: any) => (
              <div
                key={block.globalIndex}
                style={{
                  background: hoverIdx === block.globalIndex ? 'rgba(19,192,138,0.08)' : '#fafbfc',
                  border: hoverIdx === block.globalIndex ? '1.5px solid #13C08A' : '1px solid #eee',
                  borderRadius: 6,
                  padding: 12,
                  marginBottom: 10,
                  cursor: 'pointer',
                  transition: 'all 0.2s',
                  position: 'relative',
                }}
                onMouseEnter={() => {
                  setHoverIdx(block.globalIndex);
                  // 悬停时自动滚动到对应PDF页
                  const pageEl = pageRefs.current[block.pageIndex];
                  if (pageEl) {
                    pageEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
                  }
                }}
                onMouseLeave={() => setHoverIdx(null)}
              >
                {/* 页码标识 */}
                <div
                  style={{
                    position: 'absolute',
                    top: 8,
                    right: 8,
                    background: '#f0f0f0',
                    color: '#666',
                    padding: '2px 6px',
                    borderRadius: 3,
                    fontSize: 10,
                    fontWeight: 500,
                  }}
                >
                  P{block.pageIndex + 1}
                </div>
                <div style={{ paddingRight: 30 }}>
                  {/* 判断是否为表格块 */}
                  {block.type === 'table' && block.blocks ? (
                    <div>
                      {/* 渲染表格标题 */}
                      {block.blocks
                        .filter((b: any) => b.type === 'table_caption')
                        .map((caption: any, idx: number) => (
                          <div key={idx} style={{ fontWeight: 600, marginBottom: 8 }}>
                            {caption.lines?.[0]?.spans?.[0]?.content}
                          </div>
                        ))}
                      {/* 渲染表格主体 */}
                      {block.blocks
                        .filter((b: any) => b.type === 'table_body')
                        .map((body: any, idx: number) => {
                          const html = body.lines?.[0]?.spans?.[0]?.html;
                          return html ? (
                            <div
                              key={idx}
                              style={{ overflowX: 'auto' }}
                              dangerouslySetInnerHTML={{ __html: html }}
                            />
                          ) : null;
                        })}
                    </div>
                  ) : (
                    // 普通文本块
                    block.lines
                      ?.map((l: any) => l.spans?.map((s: any) => s.content).join(''))
                      .join(' ')
                  )}
                </div>
              </div>
            ))}
            {allBlocks.length === 0 && (
              <div
                style={{
                  textAlign: 'center',
                  color: '#999',
                  fontSize: 14,
                  marginTop: 40,
                }}
              >
                暂无解析内容
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PDFDetailPage;
