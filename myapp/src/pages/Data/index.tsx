import React, { useState, useEffect } from 'react';
import SearchActionBar from '@/components/SearchActionBar';
import { PageContainer } from '@ant-design/pro-components';
import ImportUnstructuredModal from './ImportUnstructuredModal';
import ImportStructuredModal from './ImportStructuredModal';
import './index.less';
import DataSide from './DataSide';
import DataTable from './DataTable';
import { Button, Modal, message } from 'antd';
import { batchDeleteUnstructuredData, fetchMineruDetail, downloadStructuredData } from '@/services/api/dataCategory';
import PDFDetailPage from './PDFDetailPage';
import { LeftOutlined } from '@ant-design/icons';

const DataList: React.FC = () => {
  const [latestImportDate, setLatestImportDate] = useState<string>('');
  const [currentCategoryId, setCurrentCategoryId] = useState('');
  const [currentCategoryName, setCurrentCategoryName] = useState('');
  const [currentCategoryDesc, setCurrentCategoryDesc] = useState('');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [batchDeleteModalVisible, setBatchDeleteModalVisible] = useState(false);
  const [tableRefreshKey, setTableRefreshKey] = useState(0);
  const [pdfDetailInfo, setPdfDetailInfo] = useState<{ files: any[]; currentFile: any } | null>(
    null,
  );
  const [selectedSideKeys, setSelectedSideKeys] = useState<React.Key[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [lastExpandedKeys, setLastExpandedKeys] = useState<React.Key[]>([]);
  const [isBackFromDetail, setIsBackFromDetail] = useState(false);
  const [treeData, setTreeData] = useState<any[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [fileLoading, setFileLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'unstructured' | 'structured'>('unstructured');

  // 递归查找父级key链
  function findParentKeys(
    tree: any[],
    targetKey: React.Key,
    path: React.Key[] = [],
  ): React.Key[] | null {
    for (const node of tree) {
      if (node.key === targetKey) return path;
      if (node.children) {
        const res = findParentKeys(node.children, targetKey, [...path, node.key]);
        if (res) return res;
      }
    }
    return null;
  }

  // 递归查找节点信息
  function findNodeInfo(tree: any[], targetKey: React.Key): any | null {
    for (const node of tree) {
      if (node.key === targetKey) return node;
      if (node.children) {
        const res = findNodeInfo(node.children, targetKey);
        if (res) return res;
      }
    }
    return null;
  }

  // 搜索
  const handleQuery = () => {
    setSearchKeyword(searchKeyword);
  };

  const handleReset = () => {
    setSearchKeyword('');
  };

  // 批量删除点击事件
  const handleBatchDelete = () => {
    if (selectedRowKeys.length === 0) return;
    setBatchDeleteModalVisible(true);
  };

  // 批量删除确认
  const handleBatchDeleteConfirm = async () => {
    try {
      const result = await batchDeleteUnstructuredData(selectedRowKeys as string[]);
      if (result.success) {
        message.success('批量删除成功');
        setSelectedRowKeys([]);
        setTableRefreshKey((k) => k + 1);
      } else {
        message.error(result.message || '批量删除失败');
      }
    } catch (error) {
      message.error('批量删除请求失败');
    } finally {
      setBatchDeleteModalVisible(false);
    }
  };

  useEffect(() => {
    if (currentCategoryId) {
      setSelectedSideKeys([currentCategoryId]);
    }
  }, [currentCategoryId]);

  // 监听详情页返回，恢复上次展开
  useEffect(() => {
    if (isBackFromDetail) {
      // 自动展开当前categoryId的父级链，包含当前节点
      if (treeData && currentCategoryId) {
        const parentKeys = findParentKeys(treeData, currentCategoryId) || [];
        const fullExpandedKeys = [...parentKeys, currentCategoryId];
        setExpandedKeys(fullExpandedKeys);
        setSelectedSideKeys([currentCategoryId]);

        // 查找并设置当前分类的信息
        const nodeInfo = findNodeInfo(treeData, currentCategoryId);
        if (nodeInfo) {
          setCurrentCategoryName(nodeInfo.rawTitle || nodeInfo.title);
          setCurrentCategoryDesc(nodeInfo.description || '');
        }
      } else if (lastExpandedKeys.length > 0) {
        // 如果没有当前分类ID，则恢复之前的展开状态
        setExpandedKeys(lastExpandedKeys);
      }
      setIsBackFromDetail(false);
      setIsInitialized(true); // 标记为已初始化，避免重复初始化
    }
  }, [isBackFromDetail, lastExpandedKeys, treeData, currentCategoryId]);

  // 监听树形数据变化，处理首次初始化
  useEffect(() => {
    if (treeData.length > 0 && !isInitialized && !isBackFromDetail) {
      // 首次进入页面，默认选中并展开根目录
      const rootKey = treeData[0]?.key;
      if (rootKey) {
        setSelectedSideKeys([rootKey]);
        setExpandedKeys([rootKey]);
        setLastExpandedKeys([rootKey]);
        setCurrentCategoryId(rootKey);
        setIsInitialized(true);
      }
    }
  }, [treeData, isInitialized, isBackFromDetail]);

  return (
    <PageContainer
      ghost
      className="data-page"
      header={
        pdfDetailInfo
          ? {
              title: (
                <span style={{ display: 'flex', alignItems: 'center' }}>
                  <LeftOutlined
                    style={{ fontSize: 18, marginRight: 8, cursor: 'pointer' }}
                    onClick={() => {
                      setPdfDetailInfo(null);
                      setIsBackFromDetail(true); // 标记为详情页返回
                    }}
                  />
                  详情
                </span>
              ),
              breadcrumb: undefined,
            }
          : undefined
      }
    >
      {pdfDetailInfo ? (
        <PDFDetailPage
          files={pdfDetailInfo.files || []}
          currentFile={pdfDetailInfo.currentFile}
          onFileSelect={async (file) => {
            if (fileLoading || file.id === pdfDetailInfo.currentFile.id) return;
            setFileLoading(true);
            try {
              const detail = await fetchMineruDetail(file.id);
              // 兼容接口返回结构，假设detail.result为文件详情
              const fileDetail = detail.result || detail;
              setPdfDetailInfo((info) =>
                info ? { ...info, currentFile: { ...file, ...fileDetail } } : info,
              );
            } catch (e) {
              // message.error('获取文件详情失败');
            } finally {
              setFileLoading(false);
            }
          }}
        />
      ) : (
        <div className="data-page-container">
          {/* 左侧树形结构 */}
          <DataSide
            selectedKeys={selectedSideKeys}
            onSelectedKeysChange={setSelectedSideKeys}
            expandedKeys={expandedKeys}
            onExpandedKeysChange={setExpandedKeys}
            onTreeDataChange={setTreeData}
            onCategoryChange={(id, name, desc) => {
              setCurrentCategoryId(id);
              setCurrentCategoryName(name);
              setCurrentCategoryDesc(desc);
              setSelectedSideKeys([id]);
            }}
            onTabChange={(tab: 'unstructured' | 'structured') => {
              setActiveTab(tab);
              setSelectedRowKeys([]);
            }}
          />
          {/* 右侧内容区域 */}
          <div className="data-page-content">
            <div className="data-page-content-title-bar">
              <div className="data-page-content-title">{currentCategoryName || '数据目录'}</div>
              <div className="data-page-content-subtitle">
                {currentCategoryDesc || '暂无描述信息'}
              </div>
            </div>
            <div
              className="data-page-content-header"
              style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
            >
              <SearchActionBar
                searchKeyword={searchKeyword}
                onKeywordChange={setSearchKeyword}
                onQuery={handleQuery}
                onReset={handleReset}
                onPressEnter={handleQuery}
                prefix={
                  <img
                    src="/icons/search.svg"
                    alt="search"
                    style={{ width: 18, height: 18, marginRight: 4 }}
                  />
                }
              />
              <div style={{ display: 'flex', alignItems: 'center' }}>
                {selectedRowKeys.length > 0 && (
                  <Button
                    className="data-page-content-batch-delete"
                    style={{ height: 40, marginRight: 8 }}
                    onClick={handleBatchDelete}
                  >
                    <img src="/icons/remove.svg" alt="remove" style={{ width: 18, height: 18 }} />
                    批量删除
                  </Button>
                )}
                {activeTab === 'structured' && currentCategoryId !== 'structured_root' && latestImportDate && (
                  <>
                    <span
                      style={{
                        height: 40,
                        marginRight: 8,
                        display: 'flex',
                        alignItems: 'center',
                        background: 'transparent',
                        border: 'none',
                        boxShadow: 'none',
                        cursor: 'default',
                        padding: 0,
                        color: '#939CAF',
                        fontSize: 14,
                        fontWeight: 400,
                        lineHeight: '22px',
                      }}
                    >
                      <img src="/icons/time.svg" alt="time" style={{ width: 18, height: 18, marginRight: 4 }} />
                      <span>{new Date(latestImportDate).toLocaleString()}</span>
                    </span>
                    <Button
                      style={{ height: 40, marginRight: 8, display: 'flex', alignItems: 'center' }}
                      onClick={async () => {
                        try {
                          const page = 1;
                          const pageSize = 10;
                          const blob = await downloadStructuredData({
                            tableId: currentCategoryId,
                            keywords: searchKeyword,
                            page,
                            pageSize,
                          });
                          const url = window.URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `${currentCategoryName || '结构化数据'}.xlsx`;
                          document.body.appendChild(a);
                          a.click();
                          a.remove();
                          window.URL.revokeObjectURL(url);
                        } catch (e) {
                          message.error('下载失败');
                        }
                      }}
                    >
                      <img src="/icons/download.svg" alt="download" style={{ width: 18, height: 18, marginRight: 4 }} />
                      下载
                    </Button>
                  </>
                )}
                <Button
                  type="primary"
                  style={{ height: 40 }}
                  onClick={() => {
                    // 结构化数据下，如果选择的是所有数据表（根目录），给出提示
                    if (activeTab === 'structured' && currentCategoryId === 'structured_root') {
                      message.warning('请选择具体的数据表，再导入数据');
                      return;
                    }
                    setImportModalVisible(true);
                  }}
                >
                  <img
                    src="/icons/add_02.svg"
                    alt="add"
                    style={{ width: 18, height: 18, filter: 'brightness(0) invert(1)' }}
                  />
                  导入数据
                </Button>
              </div>
            </div>
            <DataTable
              categoryId={currentCategoryId}
              searchKeyword={searchKeyword}
              onSelectedRowKeysChange={setSelectedRowKeys}
              refreshKey={tableRefreshKey}
              activeTab={activeTab}
              onShowPDFDetail={(files, file) => {
                // 进入详情页前记录当前展开状态
                setLastExpandedKeys([...expandedKeys]);
                setPdfDetailInfo({ files, currentFile: file });
              }}
              onLatestImportDateChange={setLatestImportDate}
            />
          </div>
        </div>
      )}
      {activeTab === 'unstructured' ? (
        <ImportUnstructuredModal
          open={importModalVisible}
          onCancel={() => setImportModalVisible(false)}
          onOk={() => {
            setImportModalVisible(false);
          }}
          onRefreshTable={() => setTableRefreshKey((k) => k + 1)}
          categoryName={currentCategoryName}
          categoryId={currentCategoryId}
        />
      ) : (
        <ImportStructuredModal
          open={importModalVisible}
          onCancel={() => setImportModalVisible(false)}
          categoryName={currentCategoryName}
          categoryId={currentCategoryId}
          onSuccess={() => {
            // 导入成功后刷新表格数据
            setTableRefreshKey((k) => k + 1);
          }}
        />
      )}
      <Modal
        open={batchDeleteModalVisible}
        onCancel={() => setBatchDeleteModalVisible(false)}
        onOk={handleBatchDeleteConfirm}
        okText="确定"
        cancelText="取消"
        title="批量删除"
      >
        确定要批量删除选中的数据吗？
      </Modal>
    </PageContainer>
  );
};

// @ts-ignore
export default DataList;
