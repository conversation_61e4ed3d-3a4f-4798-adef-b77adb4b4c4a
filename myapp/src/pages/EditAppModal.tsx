import { PlusOutlined } from '@ant-design/icons';
import { Form, Input, Modal, Upload, message } from 'antd';
import type { RcFile, UploadProps } from 'antd/es/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import React, { useEffect, useState } from 'react';

interface EditAppModalProps {
  visible: boolean;
  onCancel: () => void;
  onOk: (values: any) => void;
  initialValues?: any;
}

/**
 * 编辑应用的Modal组件
 * @param visible 是否可见
 * @param onCancel 取消回调
 * @param onOk 确定回调
 * @param initialValues 初始化数据
 */
const EditAppModal: React.FC<EditAppModalProps> = ({ visible, onCancel, onOk, initialValues }) => {
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [fileDelete, setFileDelete] = useState(0);

  const beforeUpload = (file: RcFile) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片大小不能超过 2MB!');
      return false;
    }
    return true;
  };

  const handleChange: UploadProps['onChange'] = ({ file, fileList: newFileList }) => {
    // 当删除图标时，file.status 会是 'removed'
    if (file.status === 'removed') {
      message.info('已删除应用图标');
      setFileDelete(1);
    } else if (newFileList.length > 0) {
      setFileDelete(0); // 有新上传文件时重置为0
    }
    setFileList(newFileList);
  };

  const getAppTypeDesc = (type: string) => {
    if (!type) return '';
    return type.toString() === '2' ? '工作流' : '智能体';
  };

  useEffect(() => {
    if (visible && initialValues) {
      form.setFieldsValue({
        name: initialValues.name,
        description: initialValues.description,
        type: getAppTypeDesc(initialValues.type),
      });
      if (initialValues.icon) {
        setFileList([
          {
            uid: '-1',
            name: 'image.png',
            status: 'done',
            url: initialValues.icon,
          },
        ]);
        setFileDelete(0); // 初始化时如果有图标，设为0
      }
    } else {
      form.resetFields();
      setFileList([]);
      setFileDelete(0); // 关闭弹窗时重置
    }
  }, [visible, initialValues, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const formData = new FormData();
      formData.append('name', values.name);
      formData.append('description', values.description || '');
      formData.append('type', initialValues?.type ? String(initialValues.type) : '');

      // 使用状态中的fileDelete值(保持数字类型)
      formData.append('fileDelete', fileDelete.toString());

      if (initialValues?.flowId) {
        formData.append('flowId', initialValues.flowId);
      }

      if (fileList.length > 0 && fileList[0].originFileObj) {
        formData.append('file', fileList[0].originFileObj);
      }

      onOk(formData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Modal title="编辑应用" open={visible} onCancel={onCancel} onOk={handleOk} destroyOnHidden>
      <Form form={form} layout="vertical">
        <Form.Item label="应用类型" name="type">
          <Input disabled style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="name"
          label="应用名称"
          rules={[
            { required: true, message: '请输入应用名称' },
            { max: 30, message: '名称不能超过30个字符' },
          ]}
        >
          <Input placeholder="请输入应用名称" maxLength={30} />
        </Form.Item>

        <Form.Item
          name="description"
          label="应用描述"
          rules={[{ max: 100, message: '描述不能超过100个字符' }]}
        >
          <Input.TextArea placeholder="请输入应用描述" maxLength={100} />
        </Form.Item>

        <Form.Item label="应用图标">
          <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            maxCount={1}
          >
            {fileList.length === 0 && (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            )}
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditAppModal;
