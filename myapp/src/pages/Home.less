.add-card {
  border: 1px dashed #d9d9d9;
  background: transparent;
  border-radius: 2px;
  transition: border-color 0.2s;
}
.add-card:hover {
  border-color: #0AAC79 !important;
}
.add-card:hover .add-card-content,
.add-card:hover .add-card-content p {
  color: #0AAC79 !important;
}

.add-card:hover {
  border-color: #0AAC79 !important;
  transform: translateY(-2px);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.add-card:hover .anticon {
  color: #0AAC79 !important;
}

.add-card:hover p {
  color: #0AAC79 !important;
}

/* 应用卡片hover效果 */
.ant-card.ant-card-hoverable:hover {
  transform: translateY(-2px);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s ease;
}

.add-card .ant-card-body {
  background: #ffffff;
}
.ant-card{
  .ant-card-meta-description{
    // display: -webkit-box;
    // -webkit-box-orient: vertical;
    // -webkit-line-clamp: 3; /* 限制为两行 */
    // overflow: hidden;
    // min-height: 75px;
    div:first-child{
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; /* 限制为两行 */
      overflow: hidden;
    }
  }
}
.ant-card .ant-card-body{
  padding: 30px 20px 0;
  min-height: 75%;
}
.ant-pro-layout .ant-pro-layout-content {
  padding: 0;
}

.ant-card .ant-card-actions{
  background: rgb(246, 247, 251);
}

.ant-card .ant-card-meta-detail >div:not(:last-child){
  margin-bottom: 5px;
}

.ant-card .ant-card-meta-avatar {
  padding-inline-end: 10px;
}

/* 全局禁用所有antd Radio组件点击效果 */
.ant-radio-group,
.ant-radio-button-wrapper {
  -webkit-tap-highlight-color: transparent !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  pointer-events: auto !important;
  cursor: pointer !important;

  &:active,
  &:focus,
  &:hover {
    -webkit-tap-highlight-color: transparent !important;
    transform: none !important;
    animation: none !important;
    transition: none !important;
  }

  /* 禁用所有点击反馈效果 */
  &::after {
    display: none !important;
    content: none !important;
    animation: none !important;
    box-shadow: none !important;
  }
}

/* 全局禁用antd Radio的所有交互状态 */
.ant-radio-group {
  * {
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
  }

  &:focus-within {
    outline: none !important;
    box-shadow: none !important;
  }
}

/* 禁用所有Radio相关的伪元素效果 */
.ant-radio,
.ant-radio-input,
.ant-radio-inner {
  -webkit-tap-highlight-color: transparent !important;

  &:active,
  &:focus {
    transform: none !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

/* 全局禁用所有antd Tabs组件切换效果 */
.ant-tabs {
  -webkit-tap-highlight-color: transparent !important;

  .ant-tabs-tab {
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    transition: none !important;

    &:active,
    &:focus,
    &:hover {
      -webkit-tap-highlight-color: transparent !important;
      transform: none !important;
      animation: none !important;
      transition: none !important;
      box-shadow: none !important;
      outline: none !important;
    }

    .ant-tabs-tab-btn {
      -webkit-tap-highlight-color: transparent !important;
      user-select: none !important;
      transition: none !important;

      &:active,
      &:focus {
        transform: none !important;
        box-shadow: none !important;
        outline: none !important;
        -webkit-tap-highlight-color: transparent !important;
      }
    }
  }

  /* 禁用Tab切换时的墨水条动画 */
  .ant-tabs-ink-bar {
    transition: none !important;
    animation: none !important;

    &.ant-tabs-ink-bar-animated {
      transition: none !important;
      animation: none !important;
    }
  }

  /* 禁用Tab内容切换动画 */
  .ant-tabs-content {
    transition: none !important;
    animation: none !important;
  }

  .ant-tabs-tabpane {
    transition: none !important;
    animation: none !important;
  }
}

.ant-radio-button-wrapper {
  &:active {
    transform: none !important;
    box-shadow: none !important;
    background: inherit !important;
    border: inherit !important;
    color: inherit !important;
  }

  &:focus {
    transform: none !important;
    box-shadow: none !important;
    outline: none !important;
    border: inherit !important;
    background: inherit !important;
  }

  &:focus-visible {
    outline: none !important;
    box-shadow: none !important;
  }

  /* 禁用所有可能的涟漪效果和动画 */
  &::before,
  &::after {
    display: none !important;
    content: none !important;
  }

  /* 禁用按下时的所有视觉反馈 */
  &:active:not(.ant-radio-button-wrapper-disabled) {
    transform: none !important;
    scale: 1 !important;
    filter: none !important;
    opacity: 1 !important;
  }
}

/* 自定义Radio组件样式 - 取消antd默认样式 */
.custom-radio-group {
  background: #F6F7FB;
  border-radius: 8px;
  padding: 4px;
  box-shadow: none;
  border: none;
  height: 50px;
  display: flex;
  align-items: center;
  gap: 0;
  -webkit-tap-highlight-color: transparent !important;
  user-select: none !important;

  .ant-radio-button-wrapper {
    border: none !important;
    background: transparent !important;
    border-radius: 8px !important;
    font-weight: 500;
    font-size: 16px;
    height: 32px;
    line-height: 32px;
    margin-right: 0;
    padding: 0 18px;
    box-shadow: none !important;
    color: #191919;
    transition: none !important;
    -webkit-tap-highlight-color: transparent !important;
    user-select: none !important;
    -webkit-touch-callout: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;

    &:before {
      display: none !important;
    }

    &:first-child {
      border-left: none !important;
    }

    &:hover {
      color: #191919 !important;
      background: transparent !important;
    }

    &.ant-radio-button-wrapper-checked {
      background: #fff !important;
      color: #191919 !important;
      font-weight: 700;
      box-shadow: none !important;
      border: none !important;
      border-radius: 8px !important;
    }

    &:active,
    &:focus {
      background: transparent !important;
      box-shadow: none !important;
      border: none !important;
      outline: none !important;
      color: #191919 !important;
      transform: none !important;
      -webkit-tap-highlight-color: transparent !important;
      user-select: none !important;
    }

    &.ant-radio-button-wrapper-checked:active,
    &.ant-radio-button-wrapper-checked:focus {
      background: #fff !important;
      box-shadow: none !important;
      border: none !important;
      outline: none !important;
      color: #191919 !important;
      transform: none !important;
    }
  }
}
