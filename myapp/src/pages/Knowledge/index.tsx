import { PageContainer } from '@ant-design/pro-components';
import { Empty } from 'antd';
import React from 'react';

const KnowledgeList: React.FC = () => {
  return (
    <PageContainer>
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '60vh',
        }}
      >
        <Empty
          image="https://gw.alipayobjects.com/zos/antfincdn/ZHrcdLPrvN/empty.svg"
          styles={{ image: { height: 160 } }}
          description={<span>暂无知识库</span>}
        />
      </div>
    </PageContainer>
  );
};

export default KnowledgeList;
