import { mcpApi } from '@/services/api';
import {
  Avatar,
  // Breadcrumb,
  Button,
  Col,
  Modal,
  Row,
  Spin,
  message,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { PageContainer } from '@ant-design/pro-components';
import SearchActionBar from '@/components/SearchActionBar';

import type { McpItem } from '@/services/api/types';
import { ModelCard } from '@/components';

const McpList: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize] = useState<number>(12);
  const [loading, setLoading] = useState<boolean>(false);
  const [mcpList, setMcpList] = useState<McpItem[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [currentMcp, setCurrentMcp] = useState<McpItem | null>(null);
  const [detailVisible, setDetailVisible] = useState<boolean>(false);
  const [searchKeyword, setSearchKeyword] = useState<string>('');

  const fetchMcpList = async () => {
    setLoading(true);
    try {
      const { data, total } = await mcpApi.getMcpList({
        page: currentPage,
        pageSize,
        keywords: searchKeyword || undefined,
      });
      setMcpList(data);
      setTotal(total);
    } catch (error: any) {
      console.error('获取MCP列表出错:', error);
      message.error('获取MCP列表失败：' + (error.message || '未知错误'));
      setMcpList([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMcpList();
  }, [currentPage, pageSize, searchKeyword]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDetailClick = async (mcp: McpItem) => {
    try {
      const response = await mcpApi.getMcpDetail(mcp.id);

      if (!response?.result || typeof response.result !== 'object') {
        throw new Error('返回数据格式错误');
      }

      // 使用API返回的数据，只处理存在的字段
      setCurrentMcp({
        id: response.result.id,
        mcpCode: response.result.mcpCode || '',
        name: response.result.name || mcp.name || '',
        description: response.result.description || mcp.description || '',
        ...(response.result.icon && { icon: response.result.icon }),
        ...(response.result.isOnline && { isOnline: response.result.isOnline }),
      });
      setDetailVisible(true);
    } catch (error: any) {
      message.error('获取详情失败：' + (error.message || '未知错误'));
    }
  };

  const handleDetailClose = () => {
    setDetailVisible(false);
    setCurrentMcp(null);
  };

  const handleStatusChange = async (id: string, checked: boolean) => {
    try {
      await mcpApi.updateMcpStatus({
        id,
        isOnline: checked ? 1 : 0,
      });
      message.success(`状态更新成功：${checked ? '已上架' : '未上架'}`);
      setMcpList((prevList) =>
        prevList.map((item) => (item.id === id ? { ...item, isOnline: checked ? 1 : 0 } : item)),
      );
    } catch (error: any) {
      message.error('状态更新失败：' + (error.message || '未知错误'));
    }
  };

  return (
    <PageContainer>
      {/* <Breadcrumb
        items={[{ title: 'MCP' }, { title: 'MCP列表' }]}
        style={{ marginBottom: '16px' }}
      /> */}
      <SearchActionBar
        searchKeyword={searchKeyword}
        onKeywordChange={setSearchKeyword}
        onQuery={() => {
          setCurrentPage(1);
          fetchMcpList();
        }}
        onReset={() => {
          setSearchKeyword('');
          setCurrentPage(1);
        }}
        onPressEnter={() => {
          setCurrentPage(1);
          fetchMcpList();
        }}
        showStatus={false}
      />
      <Row gutter={[16, 16]} style={{marginTop:'20px'}}>
        {loading ? (
          <Col span={24}>
            <div style={{ padding: '50px', textAlign: 'center' }}>
              <Spin />
              <div style={{ marginTop: 16 }}>加载中...</div>
            </div>
          </Col>
        ) : (
          <>
            {mcpList.map((mcp: McpItem, index: number) => (
              <Col xs={24} sm={24} md={12} lg={12} key={mcp.id || index}>
                <ModelCard
                  id={mcp.id}
                  name={mcp.name}
                  description={mcp.description || ''}
                  icon={mcp.icon || ''}
                  isOnline={typeof mcp.isOnline === 'number' ? mcp.isOnline : 0}
                  onDetailClick={() => handleDetailClick(mcp)}
                  onStatusChange={(checked: boolean) => handleStatusChange(mcp.id, checked)}
                />
              </Col>
            ))}
          </>
        )}
      </Row>

      <Modal
        title="详情"
        open={detailVisible}
        onCancel={handleDetailClose}
        footer={[
          <Button key="close" onClick={handleDetailClose}>
            确定
          </Button>,
        ]}
        width={600}
      >
        {currentMcp && (
          <div style={{ padding: '24px' }}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>名称：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {currentMcp.name}
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>图标：</div>
              <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                <Avatar src={currentMcp.icon} size={48} />
              </div>
            </div>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ color: '#666', marginBottom: '8px' }}>参数：</div>
              <div
                style={{
                  background: '#f5f5f5',
                  padding: '8px',
                  borderRadius: '4px',
                  minHeight: '100px',
                }}
              >
                {currentMcp.description}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {total > 0 && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === 1}
            onClick={() => handlePageChange(currentPage - 1)}
          >
            &lt;
          </Button>
          {Array.from({ length: Math.min(5, Math.ceil(total / pageSize)) }, (_, i) => (
            <Button
              key={i + 1}
              type={currentPage === i + 1 ? 'primary' : 'default'}
              style={{ margin: '0 8px' }}
              onClick={() => handlePageChange(i + 1)}
            >
              {i + 1}
            </Button>
          ))}
          <Button
            type="link"
            style={{ margin: '0 8px' }}
            disabled={currentPage === Math.ceil(total / pageSize)}
            onClick={() => handlePageChange(currentPage + 1)}
          >
            &gt;
          </Button>
        </div>
      )}
    </PageContainer>
  );
};

export default McpList;
