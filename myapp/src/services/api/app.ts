import { request, uploadRequest } from './request';
import type { AppItem } from './types';

export async function getAppList(params: {
  pageSize?: number;
  page?: number;
  type?: string;
  keywords?: string;
  appStatus?: string;
}): Promise<{ data: AppItem[]; total: number }> {
  const response = await request<{
    success: boolean;
    result: {
      records: AppItem[];
      total: number;
    };
  }>('/rule/app/page', {
    method: 'GET',
    params,
  });

  return {
    data: response.result?.records || [],
    total: response.result?.total || 0,
  };
}

export async function getAppDetail(id: string): Promise<AppItem> {
  return request('/rule/app/detail', {
    method: 'GET',
    params: { id },
  });
}

export async function createApp(data: {
  name: string;
  description?: string;
  type: string;
  file?: File;
}): Promise<{ id: string }> {
  const formData = new FormData();
  formData.append('name', data.name);
  formData.append('description', data.description || '');
  formData.append('type', data.type);
  if (data.file) {
    formData.append('file', data.file);
  }
  return uploadRequest('/rule/app/add', formData);
}

export async function updateApp(data: {
  id: string;
  name: string;
  description?: string;
  type: string;
  flowId?: string;
  file?: File;
  fileDelete: number; // 0-不删除头像 1-删除头像
}): Promise<void> {
  const formData = new FormData();
  formData.append('id', data.id);
  formData.append('name', data.name);
  formData.append('description', data.description || '');
  formData.append('type', data.type);
  formData.append('fileDelete', String(data.fileDelete || 0));
  if (data.flowId) {
    formData.append('flowId', data.flowId);
  }
  if (data.file) {
    formData.append('file', data.file);
  }
  return uploadRequest('/rule/app/update', formData);
}

export async function deleteApp(id: string): Promise<void> {
  return request(`/rule/app/deleteById?id=${id}`, {
    method: 'DELETE',
  });
}

export async function duplicateApp(id: string): Promise<{ id: string }> {
  return request('/rule/app/duplicate', {
    method: 'POST',
    body: { id },
  });
}
