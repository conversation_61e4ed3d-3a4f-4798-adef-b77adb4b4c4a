import { request } from './request';
import type { ComponentItem } from './types';

export async function getComponentList(params: {
  page?: number;
  pageSize?: number;
  keywords?: string;
}): Promise<{ data: ComponentItem[]; total: number }> {
  const response = await request<{
    success: boolean;
    result: {
      records: ComponentItem[];
      total: number;
    };
  }>('/rule/component/page', {
    method: 'GET',
    params,
  });

  return {
    data: response.result?.records || [],
    total: response.result?.total || 0,
  };
}

export async function getComponentDetail(id: string): Promise<{
  success: boolean;
  result: ComponentItem;
}> {
  return request('/rule/component/detail', {
    method: 'GET',
    params: { id },
  });
}

export async function updateComponentStatus(params: {
  id: string;
  isOnline: number;
}): Promise<void> {
  return request('/rule/component/update', {
    method: 'POST',
    body: params,
  });
}
