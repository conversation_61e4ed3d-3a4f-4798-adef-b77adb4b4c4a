import { API_BASE_URL, API_PATHS } from './index';
import { uploadRequest, request } from './request';

/**
 * 数据目录相关API
 */
export async function fetchDataCategoryList() {
  const response = await fetch(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.LIST}`);
  const data = await response.json();
  return data;
}

export async function addDataCategory(params: {
  currentId: string;
  categoryName: string;
  description: string;
}) {
  const response = await fetch(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.ADD}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  return await response.json();
}

export async function updateDataCategory(params: {
  id: string;
  categoryName: string;
  description: string;
}) {
  const response = await fetch(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.UPDATE}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  return await response.json();
}

export async function fetchUnstructuredDataList(params: {
  categoryId: string;
  keywords: string;
  page?: number;
  pageSize?: number;
}) {
  const queryParams = new URLSearchParams();
  if (params.categoryId) queryParams.append('categoryId', params.categoryId);
  if (params.keywords) queryParams.append('keywords', params.keywords);
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());

  const response = await fetch(
    `${API_BASE_URL}${API_PATHS.DATA_CATEGORY.UNSTRUCTURED_DATA}?${queryParams.toString()}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
  return await response.json();
}

export async function preDeleteDataCategory(id: string) {
  const response = await fetch(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.PRE_DELETE}?id=${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return await response.json();
}

export async function deleteDataCategoryById(id: string) {
  const response = await fetch(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.DELETE}?id=${id}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
  });
  return await response.json();
}

export async function uploadUnstructuredData(formData: FormData) {
  return uploadRequest(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.UPLOAD}`, formData);
}

/**
 * 批量删除非结构化数据
 * @param idList 要删除的数据ID列表
 * @returns Promise<any>
 */
export async function batchDeleteUnstructuredData(idList: string[]) {
  const response = await fetch(`${API_BASE_URL}${API_PATHS.DATA_CATEGORY.BATCH_DELETE}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ idList }),
  });
  return await response.json();
}

/**
 * 删除结构化数据表格
 * @param tableId 表格ID
 * @returns Promise<any>
 */
export async function deleteStructuredDataTable(tableId: string) {
  const response = await fetch(
    `${API_BASE_URL}${API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_TABLE_DELETE}?tableId=${tableId}`,
    {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  );
  return await response.json();
}

// 确认导入接口
export const confirmImportUnstructuredData = (params: { categoryId: string; sessionId: string }) =>
  request(API_PATHS.DATA_CATEGORY.CONFIRM_IMPORT, {
    method: 'POST',
    body: params,
  });

// 删除导入文件接口
export const deleteImportFile = (params: {
  categoryId: string;
  sessionId: string;
  fileId: string;
}) =>
  request(API_PATHS.DATA_CATEGORY.DELETE_IMPORT, {
    method: 'POST',
    body: params,
  });

// 取消导入接口
export const cancelImportUnstructuredData = (params: { categoryId: string; sessionId: string }) =>
  request(API_PATHS.DATA_CATEGORY.CANCEL_IMPORT, {
    method: 'POST',
    body: params,
  });

export const fetchMineruDetail = (id: string) =>
  request(`${API_PATHS.DATA_CATEGORY.MINERU_DETAIL}?id=${id}`, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

export const fetchStructuredDataTableList = () =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_TABLE_LIST, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  });

export const addStructuredDataTable = (params: { tableName: string; tableDesc: string }) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_TABLE_ADD, {
    method: 'POST',
    body: params,
  });

export const updateStructuredDataTable = (params: {
  tableId: string;
  tableName: string;
  tableDesc: string;
}) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_TABLE_EDIT, {
    method: 'POST',
    body: params,
  });

export const fetchStructuredDataTableFieldList = (params: { tableId: string }) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_TABLE_LIST_FIELD, {
    method: 'GET',
    params: params,
  });

/**
 *检查导入数据与已有表结构配置是否对齐
 * @param params 包含tableId和文件的参数
 * @returns Promise<any>
 */
export const checkExistTableStructure = (params: { tableId: string; file: File }) => {
  const formData = new FormData();
  formData.append('tableId', params.tableId);
  formData.append('file', params.file);

  return uploadRequest(
    API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_CHECK_EXIST_TABLE_STRUCTURE,
    formData,
  );
};

/**
 * 根据文件获取表结构配置
 * @param params 包含tableId的参数
 * @returns Promise<any>
 */
export const getTableStructure = (params: { tableId: string; file: File }) => {
  const formData = new FormData();
  formData.append('tableId', params.tableId);
  formData.append('file', params.file);

  return uploadRequest(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_GET_TABLE_STRUCTURE, formData);
};

export const updateTableStructure = (params: { tableId: string; configs: any }) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_UPDATE_TABLE_STRUCTURE, {
    method: 'POST',
    body: params,
  });

export const importStructuredData = (params: { tableId: string }) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_IMPORT, {
    method: 'POST',
    body: params,
  });

export const fetchStructuredDataPageFieldConfig = (params: { tableId: string }) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_PAGE_FIELD_CONFIG, {
    method: 'GET',
    params: params,
  });

export const fetchStructuredDataPage = (params: {
  tableId: string;
  page: number;
  pageSize: number;
  keywords?: string;
}) =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_PAGE, {
    method: 'GET',
    params,
  });

export const downloadStructuredData = (params: {
  tableId: string;
  keywords?: string;
  page: number;
  pageSize: number;
}) => {
  const query = new URLSearchParams();
  query.append('tableId', params.tableId);
  if (params.keywords) query.append('keywords', params.keywords);
  query.append('page', params.page.toString());
  query.append('pageSize', params.pageSize.toString());
  return fetch(
    `${API_BASE_URL}${API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_EXPORT}?${query.toString()}`,
    {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    },
  ).then((res) => res.blob());
};

export const fetchStructuredDataFieldTypeConfig = () =>
  request(API_PATHS.DATA_CATEGORY.STRUCTURED_DATA_FIELD_TYPE_CONFIG, {
    method: 'GET',
  });
