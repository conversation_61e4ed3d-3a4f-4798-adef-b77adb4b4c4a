import { request } from './request';
import { McpItem } from './types';

export async function getMcpList(params: {
  page?: number;
  pageSize?: number;
  keywords?: string;
}): Promise<{ data: McpItem[]; total: number }> {
  const response = await request<{
    success: boolean;
    result: {
      records: McpItem[];
      total: number;
    };
  }>('/rule/mcp/page', {
    method: 'GET',
    params,
  });

  return {
    data: response.result?.records || [],
    total: response.result?.total || 0,
  };
}

export async function getMcpDetail(id: string): Promise<{
  success: boolean;
  result: McpItem;
}> {
  return request('/rule/mcp/detail', {
    method: 'GET',
    params: { id },
  });
}

export async function updateMcpStatus(params: { id: string; isOnline: number }): Promise<void> {
  return request('/rule/mcp/update', {
    method: 'POST',
    body: params,
  });
}
