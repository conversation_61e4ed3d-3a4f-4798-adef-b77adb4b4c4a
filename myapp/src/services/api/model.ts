import { request } from './request';
import type { ModelItem, CategoryItem } from './types';

export async function getModelCategories(): Promise<CategoryItem[]> {
  const response = await request<{
    success: boolean;
    result: CategoryItem[];
  }>('/rule/model/category', {
    method: 'GET',
  });

  return response.result || [];
}

export async function getModelList(params: {
  keywords?: string;
  id?: string;
}): Promise<{ data: ModelItem[]; total: number }> {
  const response = await request<{
    success: boolean;
    result: {
      records: ModelItem[];
      total: number;
    };
  }>('/rule/model/page', {
    method: 'GET',
    params,
  });

  return {
    data: response.result?.records || [],
    total: response.result?.total || 0,
  };
}

export async function getModelDetail(id: string): Promise<ModelItem> {
  return request('/rule/model/detail', {
    method: 'GET',
    params: { id },
  });
}

export async function updateModelStatus(params: { id: string; isOnline: number }): Promise<void> {
  return request('/rule/model/update', {
    method: 'POST',
    body: params,
  });
}
