export interface AppItem {
  id: string;
  name: string;
  description?: string;
  type: string; // '1' | '2'
  icon?: string;
  flowId?: string;
  appTypeDesc?: string;
  statusDesc?: string;
  createdAt: string;
  updatedAt: string;
  modifyTime?: string;
  appStatus?: number;
}

export interface ModelItem {
  id: string;
  name: string;
  description: string;
  type: string;
  subType: string;
  isOnline: number;
  icon?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ComponentItem {
  id: string;
  name: string;
  description: string;
  icon?: string;
  type?: string;
  isOnline: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface McpItem {
  id: string;
  mcpCode: string;
  name: string;
  description?: string;
  icon?: string;
  isOnline?: number;
}

export interface CategoryItem {
  id: string;
  name: string;
  icon: string;
  children: CategoryItem[] | null;
}
