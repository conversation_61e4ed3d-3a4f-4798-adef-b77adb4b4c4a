declare module 'slash2';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';
declare module 'omit.js';
declare module 'numeral';
declare module '@antv/data-set';
declare module 'mockjs';
declare module 'react-fittext';
declare module 'bizcharts-plugin-slider';

// React PDF 模块声明
declare module 'react-pdf' {
  import { ComponentType } from 'react';
  
  export interface DocumentProps {
    file?: {
      url?: string;
      data?: ArrayBuffer | Uint8Array;
    } | string;
    onLoadSuccess?: (pdf: { numPages: number }) => void;
    onLoadError?: (error: Error) => void;
    onSourceError?: (error: Error) => void;
    children?: React.ReactNode;
  }
  
  export interface PageProps {
    pageNumber: number;
    width?: number;
    height?: number;
    scale?: number;
    onLoadSuccess?: (page: any) => void;
    onLoadError?: (error: Error) => void;
    onRenderSuccess?: (page: any) => void;
    onRenderError?: (error: Error) => void;
  }
  
  export const Document: ComponentType<DocumentProps>;
  export const Page: ComponentType<PageProps>;
  
  export const pdfjs: {
    GlobalWorkerOptions: {
      workerSrc: string;
    };
  };
}

declare const REACT_APP_ENV: 'test' | 'dev' | 'pre' | false;
